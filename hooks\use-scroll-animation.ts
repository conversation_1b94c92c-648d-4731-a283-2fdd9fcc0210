"use client";

import { useEffect, useRef, useState } from "react";
import { useInView, useMotionValue, useSpring, useTransform } from "framer-motion";

interface ScrollAnimationOptions {
  threshold?: number;
  triggerOnce?: boolean;
  rootMargin?: string;
  parallaxRange?: [number, number];
  springConfig?: {
    stiffness: number;
    damping: number;
    mass: number;
  };
}

export function useScrollAnimation(options: ScrollAnimationOptions = {}) {
  const {
    threshold = 0.1,
    triggerOnce = false,
    rootMargin = "0px 0px -100px 0px",
    parallaxRange = [-50, 50],
    springConfig = { stiffness: 100, damping: 30, mass: 1 }
  } = options;

  const ref = useRef<HTMLDivElement>(null);
  const isInView = useInView(ref, {
    threshold,
    once: triggerOnce,
    margin: rootMargin
  });

  // Scroll progress for parallax effects
  const scrollY = useMotionValue(0);
  const scrollProgress = useMotionValue(0);

  // Spring animations for smooth movement
  const springY = useSpring(scrollY, springConfig);
  const springProgress = useSpring(scrollProgress, springConfig);

  // Transform values for different parallax effects
  const parallaxY = useTransform(springProgress, [0, 1], parallaxRange);
  const scale = useTransform(springProgress, [0, 1], [0.8, 1]);
  const opacity = useTransform(springProgress, [0, 1], [0, 1]);
  const rotate = useTransform(springProgress, [0, 1], [-5, 0]);

  useEffect(() => {
    const updateScrollProgress = () => {
      if (!ref.current) return;

      const element = ref.current;
      const rect = element.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Calculate scroll progress based on element position
      const elementTop = rect.top;
      const elementHeight = rect.height;

      // Progress from 0 (element entering viewport) to 1 (element leaving viewport)
      const progress = Math.max(0, Math.min(1,
        (windowHeight - elementTop) / (windowHeight + elementHeight)
      ));

      scrollProgress.set(progress);
      scrollY.set(window.scrollY);
    };

    // Throttled scroll handler for performance
    let ticking = false;
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          updateScrollProgress();
          ticking = false;
        });
        ticking = true;
      }
    };

    // Respect user's motion preferences
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    if (!prefersReducedMotion) {
      window.addEventListener('scroll', handleScroll, { passive: true });
      window.addEventListener('resize', updateScrollProgress, { passive: true });

      // Initial calculation
      updateScrollProgress();
    }

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', updateScrollProgress);
    };
  }, [scrollProgress, scrollY]);

  return {
    ref,
    isInView,
    scrollProgress: springProgress,
    parallaxY,
    scale,
    opacity,
    rotate,
    motionValues: {
      scrollY: springY,
      progress: springProgress
    }
  };
}

// Hook for staggered animations
export function useStaggeredAnimation(itemCount: number, delay: number = 0.1) {
  const [visibleItems, setVisibleItems] = useState<boolean[]>(
    new Array(itemCount).fill(false)
  );

  const ref = useRef<HTMLDivElement>(null);
  const isInView = useInView(ref, {
    threshold: 0.1,
    once: true,
    margin: "0px 0px -50px 0px"
  });

  useEffect(() => {
    if (isInView) {
      // Stagger the animation of items
      visibleItems.forEach((_, index) => {
        setTimeout(() => {
          setVisibleItems(prev => {
            const newState = [...prev];
            newState[index] = true;
            return newState;
          });
        }, index * delay * 1000);
      });
    }
  }, [isInView, delay, visibleItems]);

  return {
    ref,
    isInView,
    visibleItems
  };
}

// Hook for parallax background elements
export function useParallaxBackground(speed: number = 0.5) {
  const scrollY = useMotionValue(0);
  const y = useTransform(scrollY, [0, 1000], [0, -1000 * speed]);

  useEffect(() => {
    const updateScrollY = () => scrollY.set(window.scrollY);

    // Respect user's motion preferences
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    if (!prefersReducedMotion) {
      window.addEventListener('scroll', updateScrollY, { passive: true });
      return () => window.removeEventListener('scroll', updateScrollY);
    }
  }, [scrollY]);

  return { y, scrollY };
}
