import { PrismaClient } from "@prisma/client";
import { cache } from "react";

// Prevent multiple instances of Prisma Client in development
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const db =
  globalForPrisma.prisma ??
  new PrismaClient({
    log: ["query"],
  });

if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = db;

// ============================================================================
// CACHED DATABASE QUERIES (Following 15 Critical Errors Pattern #10)
// ============================================================================

/**
 * Get user by ID with React.cache for deduplication
 * This prevents duplicate queries in the same request
 */
export const getUserById = cache(async (id: string) => {
  return db.user.findUnique({
    where: { id },
    select: {
      id: true,
      email: true,
      name: true,
      image: true,
      emailVerified: true,
      createdAt: true,
      updatedAt: true,
    },
  });
});

/**
 * Get user by email with React.cache for deduplication
 */
export const getUserByEmail = cache(async (email: string) => {
  return db.user.findUnique({
    where: { email },
    select: {
      id: true,
      email: true,
      name: true,
      image: true,
      emailVerified: true,
      createdAt: true,
      updatedAt: true,
    },
  });
});

/**
 * Get organization by ID with members
 */
export const getOrganizationById = cache(async (id: string) => {
  return db.organization.findUnique({
    where: { id },
    select: {
      id: true,
      name: true,
      slug: true,
      description: true,
      image: true,
      createdAt: true,
      updatedAt: true,
      members: {
        select: {
          id: true,
          role: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
        },
      },
    },
  });
});

/**
 * Get organization by slug
 */
export const getOrganizationBySlug = cache(async (slug: string) => {
  return db.organization.findUnique({
    where: { slug },
    select: {
      id: true,
      name: true,
      slug: true,
      description: true,
      image: true,
      createdAt: true,
      updatedAt: true,
    },
  });
});

/**
 * Get user organizations with role
 */
export const getUserOrganizations = cache(async (userId: string) => {
  return db.organizationMember.findMany({
    where: { userId },
    select: {
      id: true,
      role: true,
      organization: {
        select: {
          id: true,
          name: true,
          slug: true,
          description: true,
          image: true,
        },
      },
    },
    orderBy: {
      createdAt: "desc",
    },
  });
});

/**
 * Check if user is member of organization
 */
export const isOrganizationMember = cache(
  async (userId: string, organizationId: string) => {
    const member = await db.organizationMember.findUnique({
      where: {
        organizationId_userId: {
          organizationId,
          userId,
        },
      },
      select: {
        id: true,
        role: true,
      },
    });

    return member;
  }
);

/**
 * Get organization invitations
 */
export const getOrganizationInvitations = cache(
  async (organizationId: string) => {
    return db.organizationInvitation.findMany({
      where: { organizationId },
      select: {
        id: true,
        email: true,
        role: true,
        expires: true,
        createdAt: true,
        invitedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  }
);

// ============================================================================
// AUDIT LOG UTILITIES
// ============================================================================

export async function createAuditLog({
  organizationId,
  userId,
  action,
  resource,
  resourceId,
  metadata,
  ipAddress,
  userAgent,
}: {
  organizationId?: string;
  userId?: string;
  action: string;
  resource: string;
  resourceId?: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}) {
  return db.auditLog.create({
    data: {
      organizationId,
      userId,
      action,
      resource,
      resourceId,
      metadata,
      ipAddress,
      userAgent,
    },
  });
}
