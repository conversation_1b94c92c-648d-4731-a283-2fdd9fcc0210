C:\Users\<USER>\boilerplate\user-guidelines\000-agent.mdc
C:\Users\<USER>\boilerplate\user-guidelines\01.md
C:\Users\<USER>\boilerplate\user-guidelines\02.md
C:\Users\<USER>\boilerplate\user-guidelines\03.md
C:\Users\<USER>\boilerplate\user-guidelines\04.md
C:\Users\<USER>\boilerplate\user-guidelines\05.md
C:\Users\<USER>\boilerplate\user-guidelines\06.md
C:\Users\<USER>\boilerplate\user-guidelines\07.md
C:\Users\<USER>\boilerplate\user-guidelines\100-naming-conventions.mdc
C:\Users\<USER>\boilerplate\user-guidelines\101-client-components.mdc
C:\Users\<USER>\boilerplate\user-guidelines\102-create-components.mdc
C:\Users\<USER>\boilerplate\user-guidelines\103-readme.mdc
C:\Users\<USER>\boilerplate\user-guidelines\104-nextjs-backend.mdc
C:\Users\<USER>\boilerplate\user-guidelines\105-nextjs-routes.mdc
C:\Users\<USER>\boilerplate\user-guidelines\106-postgres.mdc
C:\Users\<USER>\boilerplate\user-guidelines\107-server-components.mdc
C:\Users\<USER>\boilerplate\user-guidelines\108-shadcn-ui.mdc
C:\Users\<USER>\boilerplate\user-guidelines\109-streaming-components.mdc
C:\Users\<USER>\boilerplate\user-guidelines\110-styles.mdc
C:\Users\<USER>\boilerplate\user-guidelines\111-tailwind-styles.mdc
C:\Users\<USER>\boilerplate\user-guidelines\300-refactor-ref-react-19.mdc