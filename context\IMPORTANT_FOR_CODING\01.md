Okay, let's break down the 15 Next.JS errors demonstrated in the video, along with explanations and solutions.

**Video Introduction (0:00 - 0:28)**
The speaker introduces the topic: 15 common errors Next.JS developers make, which can lead to security vulnerabilities and impact user experience. He emphasizes that even senior developers can make these mistakes and that the content is based on his experience building multiple SaaS applications.

---

**Error 1: Layout Authorization Bypass (0:28 - 3:06)**

*   **Demonstration:**
    *   An `errors` route segment has a `layout.tsx` that checks for user authentication. If not authenticated, it's supposed to show "Not authorized!".
    *   Individual pages within this segment (e.g., `demo1/page.tsx`, `demo2/page.tsx`) contain sensitive data like "MY\_SECRET\_MESSAGE\_123".
    *   The speaker shows that if the auth check in `layout.tsx` is active, the page content is hidden.
    *   **The Flaw:** He comments out the auth check in `layout.tsx`. Then, using browser dev tools (Network tab) and an HTTP client (HTTPie), he shows that one can directly fetch the RSC (React Server Component) payload for a specific page (e.g., `errors/1/demo2?_rsc=...`) by sending appropriate headers (`RSC: 1`, `Next-Router-State-Tree`, etc.). This fetch bypasses the layout's auth logic entirely and exposes the sensitive data ("MY\_SECRET\_MESSAGE\_123") contained in the `page.tsx`.
*   **Explanation:** The `layout.tsx` only protects the *rendered HTML structure* it wraps. It does not inherently protect the *data fetching or rendering logic* of the individual `page.tsx` files when they are fetched as RSCs (e.g., during client-side navigation or direct RSC requests).
*   **Solution:**
    1.  **Page-Level Authorization:** Implement authorization checks directly within each `page.tsx` file that needs protection.
    2.  **Middleware:** Use Next.js Middleware to protect entire route segments. This is generally the preferred approach for route-based authorization.
*   **Visuals:**
    *   Browser showing "Not authorized!" or page content.
    *   Code: `layout.tsx` with auth check, `page.tsx` with secret message.
    *   Dev Tools: Network tab showing RSC fetch and response.
    *   HTTPie: Making a direct RSC request.
    *   Excalidraw: Illustrating how layouts wrap pages.

---

**Error 2: API Route Input Validation (3:08 - 4:07)**

*   **Demonstration:**
    *   An API route (`errors/2/api/route.ts`) handles `PATCH` requests to update feedback. It expects an `id` and a `review` score.
    *   The Prisma `updateMany` query uses `where: { id: json.id }`.
    *   **The Flaw:** The speaker sends a `PATCH` request using HTTPie without an `id` in the JSON body. The API route still processes the request and returns 200 OK. Because `json.id` is `undefined`, the `where` clause becomes empty (`where: {}`), causing Prisma to update *all* feedback records with the new review score. TypeScript type assertion (`as {id: string, review: number}`) doesn't prevent this at runtime.
*   **Explanation:** Data coming from the client (even to API routes) is untrusted. TypeScript types are compile-time checks, not runtime guarantees. Without runtime validation, incorrect or malicious data can lead to unintended database operations.
*   **Solution:** Use a validation library like Zod to parse and validate the incoming request body.
    ```typescript
    // In route.ts
    import { z } from 'zod';
    const ZodSchema = z.object({
      id: z.string(),
      review: z.number().min(1).max(5)
    });
    // ...
    const json = await request.json();
    const result = ZodSchema.safeParse(json);
    if (!result.success) {
      return NextResponse.json({ message: "Invalid JSON", errors: result.error.issues }, { status: 400 });
    }
    // Use result.data.id and result.data.review
    await prisma.feedback.updateMany({
      where: { id: result.data.id },
      data: { review: result.data.review },
    });
    ```
*   **Visuals:**
    *   Client page for updating feedback.
    *   Code: `api/route.ts` with Prisma update.
    *   Prisma Studio: Showing feedback data before and after.
    *   HTTPie: Sending a PATCH request without `id`.

---

**Error 3: Server Action Input Validation (4:07 - 6:01)**

*   **Demonstration:**
    *   Similar to Error 2, but using a Server Action (`errors/3/action.ts`) to update feedback.
    *   The server action `updateFeedback` takes parameters `id` and `review`.
    *   **The Flaw:** Again, using HTTPie, the speaker sends a POST request (how Server Actions are invoked) to the page URL, but with a malformed payload (e.g., missing `id`, or `review` value outside expected range if no validation). Without validation, this can lead to updating all records or other unexpected behavior. The TypeScript types on the server action parameters are compile-time, not runtime, protection.
*   **Explanation:** Server Actions are endpoints callable from the client. Any data sent to them must be validated on the server, just like API routes.
*   **Solution:** Use Zod (or a library like `next-safe-action` which uses Zod internally) to validate the input to the Server Action.
    ```typescript
    // In action.ts
    "use server";
    import { z } from 'zod';
    // ... (define ZodSchema as in Error 2)

    export async function updateFeedback(json: { id: string; review: number }) {
      const result = ZodSchema.safeParse(json);
      if (!result.success) {
        return { error: "Invalid JSON", issues: result.error.issues };
      }
      // ... prisma update using result.data
    }
    ```
*   **Visuals:**
    *   Client page using the server action.
    *   Code: `action.ts` with Prisma update.
    *   Prisma Studio.
    *   HTTPie: Sending POST request with manipulated data.

---

**Error 4: Server Action Error Handling & Crashing the Page (6:01 - 9:13)**

*   **Demonstration:**
    *   A server action (`errors/4/action.ts`) now includes Zod validation. If validation fails, it `throw new Error(result.error.message)`.
    *   The client page (`errors/4/page.tsx`) calls this server action.
    *   **The Flaw:** When invalid data is submitted (e.g., review score of 10 when max is 5), the `throw new Error` in the server action causes the entire page to crash and display the Next.js "Unhandled Runtime Error" overlay. The network request shows a 500 Internal Server Error.
*   **Explanation:** Throwing an unhandled error from a Server Action results in a 500 server error, which crashes the client-side rendering unless specifically caught and handled.
*   **Solution:**
    1.  **Graceful Error Return:** Instead of `throw new Error()`, return an object with an error property:
        ```typescript
        // In action.ts
        if (!result.success) {
          return { error: "Invalid input", validationErrors: result.error.flatten() };
        }
        ```
    2.  **Client-Side Handling:** On the client, check the result of the server action call:
        ```typescript
        // In page.tsx (using useMutation or a form)
        const result = await updateFeedback(params);
        if (result.error) {
          toast.error(result.error); // Or display error message in UI
          // if (result.validationErrors) console.error(result.validationErrors);
        } else {
          toast.success("Feedback updated!");
        }
        ```
    3.  **Using `next-safe-action`:** This library simplifies this by providing a structured way to define actions with Zod schemas and handles error/success states automatically, returning them in a consistent format (`data`, `serverError`, `validationErrors`).
*   **Visuals:**
    *   Page crashing with Next.js error overlay.
    *   Network tab showing 500 error.
    *   Code: `action.ts` with `throw new Error`, then modified to return an error object.
    *   Client code: `page.tsx` with `try...catch` or checking `result.error`.
    *   Browser showing toast notifications for errors/success.

---

**Error 5: Leaking Sensitive Data via Props to Client Components (9:13 - 12:05)**

*   **Demonstration:**
    *   A Server Component (`errors/5/page.tsx`) fetches a list of users from Prisma (`prisma.user.findMany()`).
    *   This full list of user objects (including all fields like `passwordHash`, `emailVerified`, etc.) is passed as a prop to a Client Component (`UserTable.tsx`, marked with `"use client"`).
    *   **The Flaw:** In the browser's console (`console.log(props.users)` in `UserTable.tsx`), the entire user objects, including `passwordHash`, are visible. More importantly, inspecting the Network tab for the initial page load (or the RSC data chunk) shows this sensitive data being sent over the wire to the client.
*   **Explanation:** When you pass data from a Server Component to a Client Component, Next.js serializes this data and includes it in the payload sent to the browser. If you pass entire database objects, all their fields (sensitive or not) are exposed.
*   **Solution:**
    1.  **Selective Data Fetching:** In the Server Component, use Prisma's `select` option to fetch only the necessary fields:
        ```typescript
        // In page.tsx (Server Component)
        const users = await prisma.user.findMany({
          select: { id: true, name: true, email: true /* only needed fields */ },
        });
        return <UserTable users={users} />;
        ```
    2.  **Update Prop Types:** Adjust the prop types in the Client Component (`UserTableProps`) to match the selected fields.
*   **Visuals:**
    *   Code: `page.tsx` fetching all user fields, then modified with `select`.
    *   Code: `UserTable.tsx` (Client Component) and its props.
    *   Browser Console: Showing logged `props.users` with sensitive data.
    *   Network Tab: Showing sensitive data in the RSC payload.

---

**Error 6: Missing `<Suspense>` for Slow Data Fetching (12:05 - 15:27)**

*   **Demonstration:**
    *   A page (`errors/6/page.tsx`) renders two Server Components: `UserTable` (fetches users, 1s delay) and `OrgTable` (fetches orgs, 5s delay).
    *   A `loading.tsx` file in the same directory provides a fallback UI.
    *   **The Flaw:** The entire page (and its `loading.tsx`) waits for the *longest* fetch (5 seconds for `OrgTable`) to complete before rendering anything other than the loader. The `UserTable` data, ready in 1s, is unnecessarily delayed.
*   **Explanation:** Next.js renders Server Components and their data fetches on the server. If a segment has a `loading.tsx`, it will be shown until all data for that segment's Server Components is ready. Without granular `<Suspense>` boundaries, the entire page waits for the slowest part.
*   **Solution:** Wrap individual slow-loading Server Components in `<Suspense>` boundaries with their own specific fallbacks (e.g., skeleton loaders).
    ```jsx
    // In page.tsx
    import { Suspense } from 'react';
    import UserTable from './UserTable';
    import OrgTable from './OrgTable';
    import Skeleton from '@/components/ui/skeleton'; // Example skeleton

    // ...
    <Suspense fallback={<Skeleton className="h-20 w-full" />}>
      <UserTable />
    </Suspense>
    <Suspense fallback={<Skeleton className="h-40 w-full" />}>
 неприятных```
    ```jsx
    <OrgTable />
    </Suspense>
    ```
    This allows the main page structure to render immediately, and each table component streams in with its data when ready, showing its skeleton loader in the meantime.
*   **Visuals:**
    *   Page with a global loader, then content appearing all at once.
    *   Code: `page.tsx` with `UserTable` and `OrgTable`.
    *   Code: `UserTable.tsx` and `OrgTable.tsx` with simulated delays.
    *   Modified code with `<Suspense>` wrappers and skeleton fallbacks.
    *   Page now showing parts loading independently.

---

**Error 7: Layout Shift Due to Incorrect `error.tsx` Placement (15:27 - 17:23)**

*   **Demonstration:**
    *   A route segment `errors/7/inner/page.tsx` intentionally throws an error after a 5-second delay.
    *   This `inner` segment has its own `layout.tsx` (displaying "Layout N\*7") and a `loading.tsx`.
    *   The `error.tsx` file is placed in the parent `errors/` directory, not in `errors/7/inner/`.
    *   **The Flaw:** When the error in `inner/page.tsx` occurs, the `error.tsx` from the *parent* (`errors/`) directory is rendered. This replaces the *entire content* of the `errors/7/inner` segment, including its "Layout N\*7", causing a jarring layout shift.
*   **Explanation:** Next.js error boundaries (`error.tsx`) catch errors in child components within their segment. If an error occurs and there's no `error.tsx` in the *current* segment, Next.js looks for one in the parent segment. The parent's error boundary will replace the whole child segment.
*   **Solution:** Place an `error.tsx` file *inside the specific segment* where the error is likely to occur (e.g., `errors/7/inner/error.tsx`). This localized `error.tsx` will then render *within* the `errors/7/inner/layout.tsx`, preserving the layout and preventing the shift.
*   **Visuals:**
    *   File structure showing `error.tsx` in parent, then moved to child.
    *   Page showing loading, then layout disappearing and parent error page.
    *   Page showing loading, then child error page *within* its layout.

---

**Error 8: Using `"use client"` Too High in the Component Tree / Not Pushing Client Components to Leaves (17:23 - 20:10)**

*   **Demonstration:**
    *   `errors/8/page.tsx` is a Server Component that renders a `UserTable` component.
    *   Initially, `UserTable.tsx` displays user data. To add an interactive "Delete" button (which uses an `onClick` handler and a `dialog.add` function, requiring client-side JavaScript), the entire `UserTable.tsx` is marked with `"use client"`.
    *   **The Flaw:** Making the entire `UserTable` a Client Component means all its JSX, any data transformation logic, and the `users` prop itself become part of the client-side JavaScript bundle and are sent to the browser. This is inefficient if only a small part (the button) needs interactivity.
*   **Explanation:** When a component is marked `"use client"`, it and all modules it imports are bundled for the client. This can lead to larger client bundles than necessary if large, mostly static components are made into Client Components just for a small piece of interactivity.
*   **Solution ("Push Client Components to the Leaves"):**
    1.  Keep `UserTable.tsx` as a Server Component (remove `"use client"`).
    2.  Extract the interactive part (the "Delete" button) into a new, smaller component, e.g., `DeleteUserButton.tsx`.
    3.  Mark `DeleteUserButton.tsx` with `"use client"`.
    4.  Pass any necessary data (like `userId`) as props to `DeleteUserButton`.
    5.  Use `DeleteUserButton` within the `map` function of the `UserTable` Server Component.
    This way, only the button is a Client Component, minimizing the JavaScript sent to the browser.
*   **Visuals:**
    *   Code: `UserTable.tsx` first as Client Component, then as Server Component.
    *   Code: New `DeleteUserButton.tsx` (Client Component).
    *   Network tab showing (implicitly) smaller JS bundle or data payload when optimized.

---

**Error 9: Hydration Mismatches with Client-Specific APIs (Date, `window`) (20:10 - 24:25)**

*   **Demonstration:**
    *   A Client Component (`errors/9/Href.tsx`) tries to display `new Date().toLocaleTimeString()` and `window.location.href`.
    *   **The Flaw:** This causes a hydration error in the browser console ("Text content does not match server-rendered HTML"). The server renders the date/time based on its timezone/locale, and `window.location.href` is undefined. The client renders the date/time based on the user's browser settings and has a defined `window.location.href`. This mismatch triggers the error.
*   **Explanation:** React expects the server-rendered HTML to be identical to the initial client-side render. APIs that behave differently on the server versus the client (like `Date` formatting based on locale, or `window` object properties) will cause mismatches.
*   **Solution:**
    1.  **`typeof window !== "undefined"` Check (Partial Fix):** For `window` object access, you can check `if (typeof window !== "undefined")`. This prevents server-side errors but might still lead to hydration mismatches if the server renders `null` and client renders a value.
    2.  **`useIsClient` Custom Hook (Preferred for many cases):**
        ```typescript
        // hooks/useIsClient.ts
        import { useEffect, useState } from 'react';
        export function useIsClient() {
          const [isClient, setIsClient] = useState(false);
          useEffect(() => { setIsClient(true); }, []);
          return isClient;
        }
        ```
        In the component:
        ```jsx
        const isClient = useIsClient();
        // ...
        {isClient && <p>Href: {window.location.href}</p>}
        {isClient ? new Date().toLocaleTimeString() : "Loading time..."} // Or null
        ```
        This ensures the initial server render and initial client render are consistent (e.g., both `null` or a placeholder for the dynamic parts). The actual client-specific value is then rendered after the `useEffect` runs.
    3.  **`usePathname()` (for URL):** For `window.location.href`, use `usePathname()` from `next/navigation`.
*   **Visuals:**
    *   Browser console showing hydration error.
    *   Code: `Href.tsx` with problematic APIs.
    *   Code: `useIsClient.ts` hook.
    *   Modified `Href.tsx` using `useIsClient`.

---

**Error 10: Duplicate Data Fetching (No `fetch` Deduplication for DB Queries) (24:25 - 27:16)**

*   **Demonstration:**
    *   `errors/10/page.tsx` fetches users using `await prisma.user.findMany()`.
    *   `errors/10/layout.tsx` *also* fetches users using the same Prisma query (e.g., to display `users.length`).
    *   A `logger.debug("getUsers")` is added inside a shared function `getUsers()` that performs the Prisma query.
    *   **The Flaw:** The "getUsers" log appears twice in the server console, indicating the database query was executed twice for a single page request.
*   **Explanation:** Next.js's extended `fetch` API automatically deduplicates identical fetch requests within a render pass. However, this deduplication does not apply to direct database client calls (like Prisma).
*   **Solution:**
    1.  **`React.cache`:** Wrap your data-fetching function with `cache` from `react`.
        ```typescript
        // users.query.ts
        import { cache } from 'react';
        import prisma from '@/lib/prisma';
        // ...
        export const getUsers = cache(async () => {
          logger.debug("getUsers call");
          return prisma.user.findMany({ select: { id: true, name: true, email: true }});
        });
        ```
        Then, `await getUsers()` can be called in both `page.tsx` and `layout.tsx`, but the actual database query will only run once per request.
    2.  **Naming convention (e.g., `.ssc.ts` or `.react.ts`):** The speaker mentions creating separate files like `users.query.ssc.ts` or `users.query.react.ts`. While file organization is good, the key for deduplication with non-`fetch` calls is `React.cache`. The naming might be a personal convention or related to a custom setup he has.
*   **Visuals:**
    *   Code: `page.tsx` and `layout.tsx` both calling the same Prisma query.
    *   Server console showing "getUsers" logged twice.
    *   Code: Modified `getUsers` function wrapped in `React.cache`.
    *   Server console now showing "getUsers" logged once.

---

**Error 11: Sequential vs. Parallel Queries (Optimizing Multiple Fetches) (27:16 - 29:21)**

*   **Demonstration:**
    *   `errors/11/page.tsx` fetches users and organizations sequentially:
        ```typescript
        const users = await prisma.user.findMany(...); // Waits
        const orgs = await prisma.organization.findMany(...); // Then fetches
        ```
*   **Explanation:** `await`ing each query sequentially creates a waterfall, where the second query only starts after the first one finishes. This is inefficient if the queries are independent.
*   **Solution (Prisma Transaction or `Promise.all`):**
    1.  **Prisma `$transaction` (for Prisma queries):** If all queries are Prisma operations, use `prisma.$transaction` to batch them. This sends them as a single database transaction, which can be more performant.
        ```typescript
        const usersQuery = prisma.user.findMany(...); // Promise, no await
        const orgsQuery = prisma.organization.findMany(...); // Promise, no await
        const [users, orgs] = await prisma.$transaction([usersQuery, orgsQuery]);
        ```
    2.  **`Promise.all` (General purpose):** For any set of independent promises:
        ```typescript
        const [users, orgs] = await Promise.all([
          prisma.user.findMany(...),
          prisma.organization.findMany(...)
        ]);
        ```
        Both solutions allow the queries to be initiated more concurrently, reducing total wait time.
*   **Visuals:**
    *   Code showing sequential `await`s.
    *   Code modified to use `prisma.$transaction`.

---

**Error 12: Environment Variable Validation and Client Exposure (29:21 - 31:08)**

*   **Demonstration:**
    *   `errors/12/page.tsx` directly uses `process.env.DATABASE_URL`.
    *   It also tries to use `env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` where `env` is imported from a validated env setup.
*   **Explanation & Flaws:**
    1.  **Lack of Validation:** `process.env` variables are not validated by default. If a variable is missing or has the wrong type, it can lead to runtime errors.
    2.  **Client Exposure:** Only environment variables prefixed with `NEXT_PUBLIC_` are exposed to the client-side bundle. Trying to access a server-side variable (like `DATABASE_URL`) in a Client Component will result in `undefined`.
*   **Solution:**
    1.  **Basic Zod Validation:** Create an `env.ts` file to define a Zod schema for your environment variables and parse `process.env` against it. This provides type safety and throws an error at build/startup if variables are missing/invalid.
        ```typescript
        // Example: errors/12/env.ts
        import { z } from 'zod';
        const EnvSchema = z.object({
          DATABASE_URL: z.string().url(),
          NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: z.string().min(1),
        });
        export const env = EnvSchema.parse(process.env);
        ```
    2.  **`@t3-oss/env-nextjs` (Recommended):** This library provides a `createEnv` function to rigorously validate and type-check environment variables, clearly separating server-side and client-side (public) variables. It ensures that server variables are not accidentally leaked to the client.
        ```typescript
        // Example: src/lib/env.ts (using t3-env)
        import { createEnv } from "@t3-oss/env-nextjs";
        import { z } from "zod";
        export const env = createEnv({
          server: { DATABASE_URL: z.string().url(), /* ... */ },
          client: { NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: z.string().min(1), /* ... */ },
          experimental__runtimeEnv: { /* needed for client vars in Next 13+ */
            NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
          },
        });
        ```
*   **Visuals:**
    *   Code showing direct `process.env` access.
    *   Code showing basic Zod validation in `env.ts`.
    *   Code showing `t3-env` setup.
    *   Browser potentially showing a blank page or error if a client component tries to access a server-only env var.

---

**Error 13: Not Using `notFound()` for Missing Resources (31:08 - 33:52)**

*   **Demonstration:**
    *   A dynamic route `errors/13/[userId]/page.tsx` fetches a user based on `params.userId`.
    *   If the user is not found (`!user`), the component returns a JSX element like `<Card><CardTitle>User not found</CardTitle></Card>`.
    *   **The Flaw:** When navigating to a URL with a non-existent `userId` (e.g., `/errors/13/dsddd`), the "User not found" message is displayed, but the HTTP status code returned by the server is 200 OK.
*   **Explanation:** Simply rendering different JSX for a "not found" state doesn't inform Next.js or search engines that the resource is actually missing. It's treated as a successfully rendered page.
*   **Solution:**
    1.  Import `notFound` from `next/navigation`.
    2.  When the resource is not found, call the `notFound()` function. This will interrupt rendering and display the closest `not-found.tsx` file.
        ```typescript
        import { notFound } from 'next/navigation';
        // ...
        if (!user) {
          notFound(); // This will render not-found.tsx and return a 404 status
        }
        // ... render user data
        ```
    3.  Create a `not-found.tsx` file in the same segment (or a parent segment) to define the custom UI for 404 pages.
    This ensures the correct 404 HTTP status code is returned.
*   **Visuals:**
    *   Page showing "User not found" message.
    *   Network tab showing 200 OK status, then 404 status after fix.
    *   Code: `page.tsx` returning JSX, then modified to call `notFound()`.
    *   Code: `not-found.tsx` file.

---

**Error 14: Exposing Detailed Error Messages in Production (33:52 - 35:43)**

*   **Demonstration:**
    *   In `errors/14/[userId]/page.tsx`, if a user is not authenticated, an error is thrown: `throw new Error("You must be logged in to access this page");`.
    *   The corresponding `error.tsx` for this segment displays `error.name` and `error.message`.
    *   In development mode, this shows the specific error message.
    *   **The Flaw (Production):** After building (`pnpm build`) and running in production mode (`pnpm start`), the same error now displays a generic message: "Error: An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details."
*   **Explanation:** Next.js sanitizes error messages from Server Components in production to prevent leaking potentially sensitive internal details to the client. The `error.digest` property is provided to help correlate client-side errors with server-side logs.
*   **Solution:**
    1.  Do not display `error.message` or `error.name` directly to the user in `error.tsx` in production.
    2.  Provide generic, user-friendly messages in your error boundary UI.
    3.  Log the detailed error (including `error.message`, `error.stack`, and `error.digest`) on the server-side (e.g., within a `useEffect` in `error.tsx` that calls a logging service or `console.error` which might be picked up by your hosting platform).
*   **Visuals:**
    *   Error page in dev showing specific message.
    *   Command line: `pnpm build`, `pnpm start`.
    *   Error page in prod showing generic message.
    *   Code: `error.tsx` initially showing `error.message`, then implicitly suggesting to log it instead.

---

**Error 15: Importing Server-Only Modules into Client Components (35:43 - 37:02)**

*   **Demonstration:**
    *   `errors/15/page.tsx` is a Client Component (`"use client"`).
    *   It imports `secretMethod` from a separate file, `secret.ts`.
    *   Initially, `secret.ts` just exports a simple function.
    *   **The Flaw:** The speaker modifies `secret.ts` by adding `import "server-only";` at the top. This marks the entire `secret.ts` module as server-only. Now, when the Client Component `page.tsx` tries to import from `secret.ts`, the Next.js build fails with an error indicating that a server-only component is being imported into a client context.
*   **Explanation:** The `"server-only"` package is a mechanism to ensure that certain modules (e.g., those accessing databases directly or using server-specific APIs/secrets) are never bundled and sent to the client. Importing such a module into any part of the client bundle (a `"use client"` component or its dependencies) will result in a build-time error. Prisma client and Stripe SDK (server-side part) often behave this way by default.
*   **Solution:**
    1.  **Never import server-only modules into Client Components.**
    2.  If a Client Component needs data or functionality from a server-only module, it must obtain it through:
        *   Props passed down from a parent Server Component.
        *   Calling a Server Action.
        *   Fetching from an API route.
*   **Visuals:**
    *   Code: `page.tsx` (Client Component) importing `secretMethod`.
    *   Code: `secret.ts` without, then with `import "server-only";`.
    *   Build error message in the terminal.
    *   The speaker also briefly shows using Prisma client (`prisma.user.findFirst()`) inside `secret.ts`, which would also make it server-only by default in many setups.

---

**Conclusion (37:21 - End)**
The speaker wraps up, encouraging viewers to comment and check out his Next.JS course (linked in the description) for a deeper understanding.

This detailed breakdown covers the essence of each error, the speaker's demonstration, the underlying reason for the error, and the recommended solutions.