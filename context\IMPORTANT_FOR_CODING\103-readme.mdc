---
description: ENFORCE README.md standards to ensure comprehensive project documentation
globs: **/README.md
---

# README Standards

## Context
- When creating or updating project documentation
- When adding new features or dependencies
- When changing project structure
- When updating installation or usage instructions

## Requirements

### Core Structure
- Title and Description
  - Project name
  - Clear, concise description of purpose
  - Status badges (build, coverage, version)

- Table of Contents
  - Auto-generated for READMEs longer than 2 screens
  - Hierarchical structure with clickable links

- Project Overview
  - Problem statement
  - Solution overview
  - Key features
  - Technologies used with versions
  - Architecture diagram (if applicable)

- Getting Started
  - Prerequisites
  - Installation steps
  - Environment setup
  - Configuration

- Usage
  - Basic examples
  - Common use cases
  - API documentation (if applicable)
  - Screenshots/GIFs for UI features

- Development
  - Setup instructions
  - Testing procedures
  - Contribution guidelines
  - Code style guide
  - Branch naming conventions

- Project Structure
  - Directory layout
  - Key files and their purposes
  - Architecture overview

- Deployment
  - Build instructions
  - Deployment procedures
  - Environment variables
  - Infrastructure requirements

- Maintenance
  - Update procedures
  - Troubleshooting guide
  - Known issues
  - FAQ

### Formatting
- Use proper Markdown syntax
- Include syntax highlighting for code blocks
- Keep line length under 120 characters
- Use relative links for internal references
- Include alt text for images

### Updates
- Keep version numbers current
- Update documentation with each feature
- Maintain changelog
- Review and update quarterly

## Examples

<example>
# CV Generator
@![Build Status](mdc:https:/travis-ci.org/user/cv-generator)
@![Coverage Status](mdc:https:/coveralls.io/github/user/cv-generator?branch=master)
@![Version](mdc:https:/www.npmjs.com/package/cv-generator)

Modern CV generator supporting JSON Resume format with real-time preview.

## Features
- JSON Resume format support
- Real-time preview
- Multiple themes
- PDF export
- Responsive design

## Quick Start
```bash
pnpm install
pnpm dev
```

@View full documentation
</example>

<example type="invalid">
cv generator app

install:
npm install
npm start

made by john
</example>

## Critical Notes
<critical>
- NEVER commit sensitive information (API keys, credentials)
- ALWAYS update version numbers when making changes
- ENSURE all commands and procedures are tested before documenting
- MAINTAIN synchronization between code and documentation
</critical>
