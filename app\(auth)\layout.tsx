import { Shield } from "lucide-react";
import Link from "next/link";
import { AuthErrorBoundary } from "@/components/auth/auth-error-boundary";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="auth-container">
      {/* Left side - Branding */}
      <div className="auth-branding">
        <div className="mx-auto w-full min-w-sm">
          <Link href="/" className="flex items-center space-x-2 mb-8 transition-colors hover:text-primary">
            <Shield className="h-8 w-8 text-primary" />
            <span className="text-2xl font-bold">TikPay</span>
          </Link>
          <h1 className="text-3xl font-bold tracking-tight">
            Secure payment processing for the modern web
          </h1>
          <p className="mt-4 text-lg text-muted-foreground">
            Join thousands of businesses already using TikPay to accept payments globally with confidence.
          </p>
          <div className="mt-8 space-y-4">
            <div className="flex items-center space-x-3 group">
              <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full transition-transform group-hover:scale-110" />
              <span className="text-sm text-muted-foreground">Bank-level security</span>
            </div>
            <div className="flex items-center space-x-3 group">
              <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full transition-transform group-hover:scale-110" />
              <span className="text-sm text-muted-foreground">99.99% uptime guarantee</span>
            </div>
            <div className="flex items-center space-x-3 group">
              <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full transition-transform group-hover:scale-110" />
              <span className="text-sm text-muted-foreground">24/7 support</span>
            </div>
          </div>
        </div>
      </div>

      {/* Right side - Auth forms */}
      <div className="auth-form-container">
        <div className="auth-form-wrapper">
          <div className="lg:hidden mb-8">
            <Link href="/" className="flex items-center space-x-2 transition-colors hover:text-primary">
              <Shield className="h-6 w-6 text-primary" />
              <span className="text-xl font-bold">TikPay</span>
            </Link>
          </div>
          <AuthErrorBoundary>
            {children}
          </AuthErrorBoundary>
        </div>
      </div>
    </div>
  );
}
