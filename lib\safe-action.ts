import { createSafeActionClient } from "next-safe-action";
import { auth } from "./auth";
import { db } from "./db";
import { headers } from "next/headers";

/**
 * Base safe action client
 * Provides error handling and validation
 */
export const actionClient = createSafeActionClient({
  handleReturnedServerError(e) {
    // Log the error for debugging
    console.error("Action error:", e);
    
    // Return a generic error message to the client
    // This prevents sensitive information from leaking (Critical Error #14)
    if (process.env.NODE_ENV === "development") {
      return e.message;
    }
    
    return "An unexpected error occurred. Please try again.";
  },
});

/**
 * Authenticated action client
 * Requires user to be signed in
 */
export const authActionClient = actionClient.use(async ({ next }) => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    throw new Error("You must be signed in to perform this action");
  }

  return next({
    ctx: {
      user: session.user,
      session,
    },
  });
});

/**
 * Organization action client
 * Requires user to be a member of the organization
 */
export const orgActionClient = authActionClient.use(async ({ next, ctx }) => {
  // This will be used in organization-specific actions
  // The organizationId should be passed in the action input
  return next({
    ctx: {
      ...ctx,
      // Additional organization context will be added per action
    },
  });
});

/**
 * Admin action client
 * Requires user to be an admin or owner of the organization
 */
export const adminActionClient = authActionClient.use(async ({ next, ctx }) => {
  // This will be used in admin-specific actions
  // The role check will be done per action based on organizationId
  return next({
    ctx: {
      ...ctx,
      // Additional admin context will be added per action
    },
  });
});

/**
 * Rate limited action client
 * Adds rate limiting to actions
 */
export const rateLimitedActionClient = actionClient.use(async ({ next }) => {
  // TODO: Implement rate limiting with Upstash Redis or similar
  // For now, we'll just pass through
  return next({
    ctx: {},
  });
});

/**
 * Audit logged action client
 * Automatically logs actions for audit purposes
 */
export const auditActionClient = authActionClient.use(async ({ next, ctx }) => {
  const headersList = await headers();
  const ipAddress = headersList.get("x-forwarded-for") || headersList.get("x-real-ip");
  const userAgent = headersList.get("user-agent");

  const result = await next({
    ctx: {
      ...ctx,
      audit: {
        ipAddress,
        userAgent,
      },
    },
  });

  // Log the action after execution
  // This will be implemented per action based on the specific resource
  
  return result;
});
