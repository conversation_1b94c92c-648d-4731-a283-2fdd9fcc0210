import {
  Shield,
  Zap,
  Users,
  CreditCard,
  Globe,
  Lock,
  TrendingUp,
  CheckCircle,
  Star,
  ArrowRight,
  BarChart3,
  Smartphone,
  Headphones,
  Clock,
  DollarSign,
  Layers,
  Target,
  Award,
  Crown,
  Rocket,
  Music,
  Heart,
  Camera
} from "lucide-react";

// Navigation items
export const navigation = [
  {
    id: "0",
    title: "Features",
    url: "#features",
  },
  {
    id: "1",
    title: "Pricing",
    url: "#pricing",
  },
  {
    id: "2",
    title: "How it works",
    url: "#how-it-works",
  },
  {
    id: "3",
    title: "Testimonials",
    url: "#testimonials",
  },
  {
    id: "4",
    title: "FAQ",
    url: "#faq",
  },
];

// Hero section data - TikTok Marketplace
export const heroStats = [
  {
    value: "10K+",
    label: "Accounts Sold",
  },
  {
    value: "500M+",
    label: "Total Followers",
  },
  {
    value: "50+",
    label: "Niches Available",
  },
  {
    value: "24/7",
    label: "Support",
  },
];

// Company logos for social proof - Influencer Brands
export const companyLogos = [
  "FashionNova",
  "GymShark",
  "Hype House",
  "Sephora",
  "Nike",
  "Adidas",
];

// TikTok Account Packages - Features data
export const features = [
  {
    id: "0",
    icon: Rocket,
    title: "Starter Accounts",
    description: "Perfect for beginners looking to jumpstart their TikTok presence with authentic, engaged followers.",
    benefits: [
      "1K-10K followers",
      "High engagement rates",
      "Niche-specific content",
      "Instant account transfer"
    ]
  },
  {
    id: "1",
    icon: TrendingUp,
    title: "Growth Packages",
    description: "Accelerate your influence with mid-tier accounts featuring established audiences and proven content strategies.",
    benefits: [
      "10K-50K followers",
      "Verified engagement",
      "Content history included",
      "Brand partnership ready"
    ]
  },
  {
    id: "2",
    icon: Crown,
    title: "Influencer Accounts",
    description: "Premium accounts with substantial followings and monetization opportunities already in place.",
    benefits: [
      "50K-100K followers",
      "Creator fund eligible",
      "Brand collaboration history",
      "Multiple revenue streams"
    ]
  },
  {
    id: "3",
    icon: Award,
    title: "Premium Elite",
    description: "Top-tier accounts with massive reach, established brand partnerships, and proven monetization.",
    benefits: [
      "100K+ followers",
      "Verified status available",
      "Established brand deals",
      "Premium support included"
    ]
  },
  {
    id: "4",
    icon: Target,
    title: "Niche Specialists",
    description: "Targeted accounts in specific niches with highly engaged audiences perfect for focused marketing.",
    benefits: [
      "Niche-specific followers",
      "High conversion rates",
      "Industry expertise",
      "Targeted demographics"
    ]
  },
  {
    id: "5",
    icon: Users,
    title: "Bundle Packages",
    description: "Multiple accounts across different niches to maximize your reach and diversify your influence.",
    benefits: [
      "Multiple account access",
      "Cross-niche promotion",
      "Bulk pricing discounts",
      "Coordinated strategies"
    ]
  },
];

// Benefits section - TikTok Marketplace
export const benefits = [
  {
    id: "0",
    title: "Instant Influence",
    text: "Skip the years of building followers and start with an established, engaged audience ready to convert.",
    backgroundUrl: "/assets/benefits/card-1.svg",
    iconUrl: Zap,
    imageUrl: "/assets/benefits/image-2.png",
  },
  {
    id: "1",
    title: "Proven Monetization",
    text: "Access accounts with existing revenue streams, brand partnerships, and creator fund eligibility.",
    backgroundUrl: "/assets/benefits/card-2.svg",
    iconUrl: DollarSign,
    imageUrl: "/assets/benefits/image-2.png",
    light: true,
  },
  {
    id: "2",
    title: "Niche Authority",
    text: "Establish yourself as an expert in your chosen niche with accounts that already have credibility.",
    backgroundUrl: "/assets/benefits/card-3.svg",
    iconUrl: Crown,
    imageUrl: "/assets/benefits/image-2.png",
  },
  {
    id: "3",
    title: "Secure Transfer",
    text: "All account transfers are secure, verified, and come with full ownership documentation.",
    backgroundUrl: "/assets/benefits/card-4.svg",
    iconUrl: Shield,
    imageUrl: "/assets/benefits/image-2.png",
    light: true,
  },
  {
    id: "4",
    title: "Quality Guarantee",
    text: "Every account is thoroughly vetted for authenticity, engagement quality, and growth potential.",
    backgroundUrl: "/assets/benefits/card-5.svg",
    iconUrl: Star,
    imageUrl: "/assets/benefits/image-2.png",
  },
  {
    id: "5",
    title: "Growth Support",
    text: "Get expert guidance on content strategy, audience engagement, and monetization optimization.",
    backgroundUrl: "/assets/benefits/card-6.svg",
    iconUrl: TrendingUp,
    imageUrl: "/assets/benefits/image-2.png",
  },
];

// How it works steps
export const howItWorks = [
  {
    id: "0",
    title: "Sign Up",
    description: "Create your TikPay account in minutes with our streamlined onboarding process.",
    icon: Users,
    step: "01"
  },
  {
    id: "1",
    title: "Integrate",
    description: "Add our payment gateway to your website or app with just a few lines of code.",
    icon: Layers,
    step: "02"
  },
  {
    id: "2",
    title: "Configure",
    description: "Set up your payment methods, currencies, and business rules through our dashboard.",
    icon: Target,
    step: "03"
  },
  {
    id: "3",
    title: "Go Live",
    description: "Start accepting payments immediately and watch your business grow.",
    icon: Award,
    step: "04"
  },
];

// Pricing plans
export const pricing = [
  {
    id: "0",
    title: "Starter",
    description: "Perfect for small businesses and startups",
    price: "2.9%",
    priceDescription: "per transaction",
    features: [
      "Up to $10K monthly volume",
      "Basic payment methods",
      "Standard support",
      "Basic analytics",
      "Mobile SDK",
    ],
    popular: false,
  },
  {
    id: "1",
    title: "Professional",
    description: "Ideal for growing businesses",
    price: "2.4%",
    priceDescription: "per transaction",
    features: [
      "Up to $100K monthly volume",
      "All payment methods",
      "Priority support",
      "Advanced analytics",
      "Custom branding",
      "Fraud protection",
    ],
    popular: true,
  },
  {
    id: "2",
    title: "Enterprise",
    description: "For large-scale operations",
    price: "Custom",
    priceDescription: "volume-based pricing",
    features: [
      "Unlimited monthly volume",
      "Custom payment flows",
      "Dedicated support",
      "White-label solution",
      "Advanced security",
      "Custom integrations",
    ],
    popular: false,
  },
];

// Testimonials
export const testimonials = [
  {
    id: "0",
    name: "Sarah Chen",
    position: "CTO, TechFlow",
    content: "TikPay transformed our payment processing. The integration was seamless and our conversion rates increased by 23%.",
    avatar: "/assets/testimonials/avatar-1.png",
    rating: 5,
  },
  {
    id: "1",
    name: "Marcus Rodriguez",
    position: "Founder, EcoShop",
    content: "The global payment support helped us expand to 15 new countries. TikPay's reliability is unmatched.",
    avatar: "/assets/testimonials/avatar-2.png",
    rating: 5,
  },
  {
    id: "2",
    name: "Emily Watson",
    position: "CFO, FinanceFirst",
    content: "We reduced our payment processing costs by 30% while improving security. Best decision we made this year.",
    avatar: "/assets/testimonials/avatar-3.png",
    rating: 5,
  },
];

// FAQ data
export const faq = [
  {
    id: "0",
    question: "How quickly can I start accepting payments?",
    answer: "You can start accepting payments within 24 hours of signing up. Our streamlined onboarding process and comprehensive documentation make integration quick and easy.",
  },
  {
    id: "1",
    question: "What payment methods do you support?",
    answer: "We support all major credit cards, digital wallets (Apple Pay, Google Pay, PayPal), bank transfers, and local payment methods in 150+ countries.",
  },
  {
    id: "2",
    question: "How secure is TikPay?",
    answer: "TikPay is PCI DSS Level 1 certified and uses bank-level security with 256-bit SSL encryption. We also provide advanced fraud detection and are SOC 2 Type II compliant.",
  },
  {
    id: "3",
    question: "What are your transaction fees?",
    answer: "Our fees start at 2.9% per transaction for our Starter plan, with lower rates available for higher volumes. Enterprise customers get custom volume-based pricing.",
  },
  {
    id: "4",
    question: "Do you provide customer support?",
    answer: "Yes, we offer 24/7 customer support through live chat, email, and phone. Professional and Enterprise customers get priority support and dedicated account managers.",
  },
];

// Footer links
export const footerLinks = [
  {
    title: "Product",
    links: [
      { name: "Features", href: "#features" },
      { name: "Pricing", href: "#pricing" },
      { name: "API Documentation", href: "/docs" },
      { name: "Integrations", href: "/integrations" },
    ],
  },
  {
    title: "Company",
    links: [
      { name: "About Us", href: "/about" },
      { name: "Careers", href: "/careers" },
      { name: "Blog", href: "/blog" },
      { name: "Press", href: "/press" },
    ],
  },
  {
    title: "Resources",
    links: [
      { name: "Help Center", href: "/help" },
      { name: "Contact Support", href: "/support" },
      { name: "Status Page", href: "/status" },
      { name: "Security", href: "/security" },
    ],
  },
  {
    title: "Legal",
    links: [
      { name: "Privacy Policy", href: "/privacy" },
      { name: "Terms of Service", href: "/terms" },
      { name: "Cookie Policy", href: "/cookies" },
      { name: "Compliance", href: "/compliance" },
    ],
  },
];
