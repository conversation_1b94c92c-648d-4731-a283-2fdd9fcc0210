# 📊 Codebase Analysis & Rules

This document outlines the core coding patterns, architecture decisions, and development rules for the Hexa TikPay project. All AI coders must follow these guidelines to maintain consistency and quality.

## Project Architecture

The Hexa TikPay project follows a modern Next.js architecture with the following key components:

```
📂 [project]/
├── 📂 app/                # Next.js App Router
│   ├── 📂 (auth)/         # Authentication routes
│   ├── 📂 api/            # API routes
│   ├── 📂 dashboard/      # Dashboard routes
│   └── 📂 [locale]/       # Internationalized routes
├── 📂 components/         # React components
│   ├── 📂 ui/             # UI components (shadcn/ui)
│   ├── 📂 forms/          # Form components
│   └── 📂 dashboard/      # Dashboard-specific components
├── 📂 lib/                # Utility libraries
│   ├── 📂 db/             # Database (Drizzle ORM)
│   ├── 📂 auth/           # Authentication (NextAuth.js)
│   └── 📂 validations/    # Zod validation schemas
├── 📂 public/             # Static assets
├── 📂 styles/             # Global styles
└── 📂 types/              # TypeScript type definitions
```

## Coding Standards

### Naming Conventions

1. **Files & Directories**
   - React components: PascalCase (e.g., `Button.tsx`)
   - Utilities/libraries: camelCase (e.g., `formatDate.ts`)
   - API routes: kebab-case (e.g., `user-profile.ts`)
   - Test files: `[name].test.ts` or `[name].spec.ts`

2. **Variables & Functions**
   - Variables: camelCase (e.g., `userData`)
   - Functions: camelCase (e.g., `getUserData()`)
   - React components: PascalCase (e.g., `UserProfile`)
   - Constants: UPPER_SNAKE_CASE (e.g., `MAX_RETRIES`)

3. **Types & Interfaces**
   - Interfaces: PascalCase with "I" prefix (e.g., `IUserData`)
   - Types: PascalCase (e.g., `UserData`)
   - Enums: PascalCase (e.g., `UserRole`)

### Code Structure

#### Component Structure

```tsx
import type { IUser } from '@/types';
// GOOD EXAMPLE: Well-structured component
// components/UserProfile.tsx
import { useState } from 'react';
import { Avatar } from '@/components/ui/avatar';
import { formatDate } from '@/lib/utils';

type UserProfileProps = {
  user: IUser;
  showDetails?: boolean;
};

export function UserProfile({ user, showDetails = false }: UserProfileProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  // Helper functions at the top
  const toggleExpanded = () => setIsExpanded(!isExpanded);
  const formattedDate = formatDate(user.createdAt);

  // Early returns for edge cases
  if (!user) {
    return <div>User not found</div>;
  }

  // Conditional rendering
  return (
    <div className="user-profile">
      <Avatar user={user} />
      <h2>{user.name}</h2>

      {showDetails && (
        <div>
          <p>
            Email:
            {user.email}
          </p>
          <p>
            Member since:
            {formattedDate}
          </p>
        </div>
      )}

      <button onClick={toggleExpanded}>
        {isExpanded ? 'Show Less' : 'Show More'}
      </button>

      {isExpanded && (
        <div className="expanded-details">
          {/* Additional details */}
        </div>
      )}
    </div>
  );
}
```

#### API Route Structure

```typescript
import { eq } from 'drizzle-orm';
// GOOD EXAMPLE: Well-structured API route
// app/api/users/[id]/route.ts
import { NextResponse } from 'next/server';
import { z } from 'zod';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { users } from '@/lib/db/schema';

// 1. Schema validation
const userUpdateSchema = z.object({
  name: z.string().min(2).max(100).optional(),
  email: z.string().email().optional(),
  // Other fields
});

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // 2. Authentication
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 3. Parameter validation
    const { id } = params;
    if (!id) {
      return NextResponse.json({ error: 'User ID required' }, { status: 400 });
    }

    // 4. Database query
    const user = await db.query.users.findFirst({
      where: eq(users.id, id),
    });

    // 5. Response handling
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({ user });
  } catch (error) {
    // 6. Error handling
    console.error('[GET_USER]', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Similar structure to GET
    // Authentication, validation, etc.

    const body = await request.json();
    const validationResult = userUpdateSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Update user in database
    // Return updated user
  } catch (error) {
    // Error handling
  }
}
```

## Technology Patterns

### State Management

1. **React Hooks**
   - Use `useState` for component-local state
   - Use `useReducer` for complex state logic
   - Use `useContext` for shared state across components

2. **Server Components**
   - Prefer server components for data fetching
   - Use client components only when interactivity is needed
   - Minimize client-side JavaScript

### Data Fetching

1. **Server Components**
   ```tsx
   // GOOD EXAMPLE: Data fetching in server component
   import { db } from '@/lib/db';
   import { users } from '@/lib/db/schema';

   export default async function UserList() {
     const users = await db.query.users.findMany({
       orderBy: { createdAt: 'desc' },
       limit: 10,
     });

     return (
       <ul>
         {users.map(user => (
           <li key={user.id}>{user.name}</li>
         ))}
       </ul>
     );
   }
   ```

2. **Client Components**
   ```tsx
   // GOOD EXAMPLE: Data fetching in client component
   'use client';

   import { useEffect, useState } from 'react';
   import { User } from '@/types';

   export function UserSearchResults({ query }: { query: string }) {
     const [users, setUsers] = useState<User[]>([]);
     const [isLoading, setIsLoading] = useState(false);
     const [error, setError] = useState<string | null>(null);

     useEffect(() => {
       async function fetchUsers() {
         if (!query) {
           return;
         }

         setIsLoading(true);
         setError(null);

         try {
           const response = await fetch(`/api/users/search?q=${encodeURIComponent(query)}`);

           if (!response.ok) {
             throw new Error('Failed to fetch users');
           }

           const data = await response.json();
           setUsers(data.users);
         } catch (err) {
           setError(err instanceof Error ? err.message : 'An error occurred');
         } finally {
           setIsLoading(false);
         }
       }

       fetchUsers();
     }, [query]);

     if (isLoading) {
       return <div>Loading...</div>;
     }
     if (error) {
       return (
         <div>
           Error:
           {error}
         </div>
       );
     }

     return (
       <ul>
         {users.map(user => (
           <li key={user.id}>{user.name}</li>
         ))}
       </ul>
     );
   }
   ```

## Performance Optimization

### Component Optimization

1. **Memoization**
   ```tsx
   // GOOD EXAMPLE: Properly memoized component
   import { memo } from 'react';

   type ExpensiveComponentProps = {
     data: string[];
     onItemClick: (item: string) => void;
   };

   function ExpensiveComponent({ data, onItemClick }: ExpensiveComponentProps) {
     // Expensive rendering logic
     return (
       <ul>
         {data.map(item => (
           <li key={item} onClick={() => onItemClick(item)}>
             {item}
           </li>
         ))}
       </ul>
     );
   }

   // Only re-render when props actually change
   export default memo(ExpensiveComponent);
   ```

2. **Code Splitting**
   ```tsx
   // GOOD EXAMPLE: Dynamic imports for code splitting
   import { lazy, Suspense } from 'react';

   // Lazy load heavy component
   const HeavyChart = lazy(() => import('@/components/HeavyChart'));

   export function Dashboard() {
     return (
       <div>
         <h1>Dashboard</h1>
         <Suspense fallback={<div>Loading chart...</div>}>
           <HeavyChart />
         </Suspense>
       </div>
     );
   }
   ```

### Database Optimization

1. **Efficient Queries**
   ```typescript
   import { eq } from 'drizzle-orm';
   // GOOD EXAMPLE: Optimized database query
   import { db } from '@/lib/db';
   import { posts, users } from '@/lib/db/schema';

   // Select only needed fields
   const user = await db.query.users.findFirst({
     where: eq(users.id, userId),
     columns: {
       id: true,
       name: true,
       email: true,
       // Only select needed fields
     },
   });

   // Use proper pagination
   const posts = await db.query.posts.findMany({
     where: eq(posts.userId, userId),
     limit: 10,
     offset: page * 10,
     orderBy: { createdAt: 'desc' },
   });
   ```

## Error Handling

### API Error Handling

```typescript
// GOOD EXAMPLE: Comprehensive API error handling
import { NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { db } from '@/lib/db';

export async function POST(request: Request) {
  try {
    // Normal route logic

  } catch (error) {
    // Categorize and handle different error types
    if (error instanceof ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.format() },
        { status: 400 }
      );
    }

    if (error instanceof Error && error.message.includes('Unique constraint')) {
      return NextResponse.json(
        { error: 'Resource already exists' },
        { status: 409 }
      );
    }

    // Log unexpected errors
    console.error('[API_ROUTE_NAME]', error);

    // Generic error for everything else
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

### Client Error Handling

```tsx
// GOOD EXAMPLE: Client-side error handling
'use client';

import { useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';

function ErrorFallback({ error, resetErrorBoundary }) {
  return (
    <div role="alert">
      <p>Something went wrong:</p>
      <pre>{error.message}</pre>
      <button onClick={resetErrorBoundary}>Try again</button>
    </div>
  );
}

export function UserProfile({ userId }: { userId: string }) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  async function fetchUserData() {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/users/${userId}`);

      if (!response.ok) {
        // Handle different status codes
        if (response.status === 404) {
          throw new Error('User not found');
        }

        if (response.status === 401) {
          throw new Error('You must be logged in');
        }

        throw new Error('Failed to fetch user data');
      }

      const data = await response.json();
      return data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      {/* Component content */}
    </ErrorBoundary>
  );
}
```

## Testing Standards

### Unit Testing

```typescript
// GOOD EXAMPLE: Component unit test
import { render, screen, fireEvent } from '@testing-library/react';
import { UserProfile } from '@/components/UserProfile';

describe('UserProfile', () => {
  const mockUser = {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    createdAt: new Date().toISOString(),
  };

  it('renders user information correctly', () => {
    render(<UserProfile user={mockUser} />);

    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('shows additional details when showDetails is true', () => {
    render(<UserProfile user={mockUser} showDetails={true} />);

    expect(screen.getByText(/Email:/)).toBeInTheDocument();
    expect(screen.getByText(/Member since:/)).toBeInTheDocument();
  });

  it('toggles expanded details when button is clicked', () => {
    render(<UserProfile user={mockUser} />);

    const button = screen.getByRole('button', { name: /Show More/i });
    fireEvent.click(button);

    expect(button).toHaveTextContent('Show Less');
    expect(screen.getByTestId('expanded-details')).toBeInTheDocument();

    fireEvent.click(button);
    expect(button).toHaveTextContent('Show More');
    expect(screen.queryByTestId('expanded-details')).not.toBeInTheDocument();
  });
});
```

## Common Pitfalls to Avoid

1. **Prop Drilling** - Use context or state management instead of passing props through many layers
2. **Mixing Server/Client Components** - Clearly separate server and client components
3. **Large Component Files** - Break down large components into smaller, focused ones
4. **Improper Error Handling** - Always handle errors at appropriate levels
5. **Missing Loading States** - Always provide visual feedback during loading
6. **Inadequate Validation** - Always validate inputs on both client and server
7. **Poor Performance** - Use proper memoization and code splitting
8. **Ignoring Accessibility** - Ensure components are accessible

## References

- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://react.dev/learn)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/handbook/intro.html)
- [Zod Documentation](https://zod.dev/)
- [Drizzle ORM Documentation](https://orm.drizzle.team/docs/overview)
