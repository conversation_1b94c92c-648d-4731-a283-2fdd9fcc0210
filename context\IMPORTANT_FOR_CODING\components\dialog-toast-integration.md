# 🔄 Dialog & Toast Integration Guide

Ce document explique comment intégrer et utiliser efficacement les systèmes DialogManager et ToastManager ensemble dans Hexa TikPay pour créer des expériences utilisateur cohérentes et fluides.

## 🎯 Philosophie d'Intégration

### **Principe de Complémentarité**
- **DialogManager** : Interactions qui nécessitent une réponse utilisateur (confirmations, saisies)
- **ToastManager** : Notifications et feedback qui informent sans bloquer (succès, erreurs, statuts)

### **Flux d'Interaction Typique**
1. **Action utilisateur** → **Dialog de confirmation** → **Action exécutée** → **Toast de feedback**

## 🔧 Patterns d'Intégration

### **1. Pattern : Confirmation → Action → Notification**

```typescript
// Exemple : Suppression de compte avec confirmation et feedback
import { DialogManager } from '@/components/dialog-manager/dialog-manager-store';
import { serverToast, toastSuccess, toastError } from '@/lib/server-toast';

// Server Action
export async function deleteAccountAction(accountId: string) {
  'use server';

  try {
    await deleteAccountFromDatabase(accountId);

    // Toast de succès persistant
    await toastSuccess('Account deleted successfully', {
      description: 'The account has been permanently removed',
      duration: 4000
    });

    revalidatePath('/dashboard/accounts');
    return { success: true };
  } catch (error) {
    // Toast d'erreur persistant
    await toastError('Failed to delete account', {
      description: 'Please try again or contact support',
      duration: 6000
    });
    return { success: false };
  }
}

// Client Component
function AccountCard({ account }: { account: Account }) {
  const handleDelete = () => {
    // Dialog de confirmation sécurisée
    DialogManager.deleteWithConfirm({
      title: `Delete ${account.handle}`,
      description: 'This will permanently delete the account and all associated data.',
      confirmText: account.handle,
      onDelete: async () => {
        await deleteAccountAction(account.id);
      }
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>@{account.handle}</CardTitle>
      </CardHeader>
      <CardContent>
        <Button variant="destructive" onClick={handleDelete}>
          Delete Account
        </Button>
      </CardContent>
    </Card>
  );
}
```

### **2. Pattern : Input → Validation → Feedback**

```typescript
// Exemple : Renommage avec validation et feedback
import { useClientToast } from '@/components/toast-manager/client-toasts';

function RenameAccountButton({ account }: { account: Account }) {
  const clientToast = useClientToast();

  const handleRename = () => {
    DialogManager.prompt({
      title: 'Rename Account',
      description: `Enter a new display name for @${account.handle}`,
      input: {
        label: 'Display Name',
        placeholder: account.displayName || account.handle,
        required: true,
        validation: (value) => {
          if (value.length < 3) return 'Name must be at least 3 characters';
          if (value.length > 50) return 'Name must be less than 50 characters';
          if (value === account.displayName) return 'Please enter a different name';
          return null;
        }
      },
      onSubmit: async (newName) => {
        try {
          // Feedback immédiat côté client
          clientToast.loading('Updating account name...');

          const result = await renameAccountAction(account.id, newName);

          if (result.success) {
            // Le toast de succès sera affiché par le Server Action
            clientToast.dismiss(); // Supprimer le loading toast
          } else {
            clientToast.error('Failed to rename account');
          }
        } catch (error) {
          clientToast.error('Network error occurred');
        }
      }
    });
  };

  return (
    <Button variant="outline" onClick={handleRename}>
      Rename
    </Button>
  );
}
```

### **3. Pattern : Multi-étapes avec Feedback Progressif**

```typescript
// Exemple : Processus de claim de tokens en plusieurs étapes
export async function claimTokensWorkflow(amount: string) {
  try {
    // Étape 1: Confirmation
    const confirmed = await new Promise<boolean>((resolve) => {
      DialogManager.add({
        title: 'Claim UMA Tokens',
        description: `You are about to claim ${amount} UMA tokens. This action will transfer tokens to your connected wallet.`,
        icon: CheckCircle,
        style: 'success',
        action: {
          label: `Claim ${amount} UMA`,
          onClick: () => resolve(true)
        },
        cancel: {
          label: 'Cancel',
          onClick: () => resolve(false)
        }
      });
    });

    if (!confirmed) {
      return;
    }

    // Étape 2: Traitement avec feedback
    await serverToast('Processing claim...', 'loading', {
      description: 'Please wait while we process your token claim'
    });

    // Simulation du processus
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Étape 3: Vérification wallet
    const walletConnected = await checkWalletConnection();
    if (!walletConnected) {
      await toastError('Wallet not connected', {
        description: 'Please connect your wallet to receive tokens',
        action: {
          label: 'Connect Wallet',
          onClick: 'window.open("/wallet/connect", "_blank")'
        }
      });
      return;
    }

    // Étape 4: Finalisation
    await processTokenClaim(amount);

    // Étape 5: Succès avec action
    await toastSuccess(`${amount} UMA tokens claimed!`, {
      description: 'Tokens have been transferred to your wallet',
      duration: 6000,
      action: {
        label: 'View Transaction',
        onClick: 'window.open("/dashboard/transactions", "_blank")'
      }
    });
  } catch (error) {
    await toastError('Claim failed', {
      description: 'An error occurred during the token claim process',
      duration: 6000
    });
  }
}
```

## 🎨 Patterns d'UX Avancés

### **4. Pattern : Feedback Contextuel avec Actions**

```typescript
// Exemple : Export de données avec options et feedback
function DataExportButton() {
  const handleExport = () => {
    DialogManager.add({
      title: 'Export Account Data',
      description: 'Choose the format for your data export',
      input: {
        label: 'Export Format',
        placeholder: 'CSV, JSON, or PDF',
        required: true,
        validation: (value) => {
          const formats = ['CSV', 'JSON', 'PDF'];
          if (!formats.includes(value.toUpperCase())) {
            return 'Please choose CSV, JSON, or PDF';
          }
          return null;
        }
      },
      action: {
        label: 'Start Export',
        onClick: async (format) => {
          // Toast de démarrage
          await serverToast('Preparing export...', 'loading', {
            description: 'This may take a few minutes'
          });

          try {
            const result = await exportDataAction(format.toUpperCase());

            if (result.success) {
              // Toast de succès avec téléchargement
              await toastSuccess('Export ready!', {
                description: `Your data has been exported as ${format.toUpperCase()}`,
                duration: 8000,
                action: {
                  label: 'Download',
                  onClick: `window.open("${result.downloadUrl}", "_blank")`
                }
              });
            }
          } catch (error) {
            await toastError('Export failed', {
              description: 'Please try again or contact support'
            });
          }
        }
      }
    });
  };

  return (
    <Button variant="outline" onClick={handleExport}>
      <Download className="mr-2 h-4 w-4" />
      Export Data
    </Button>
  );
}
```

### **5. Pattern : Gestion d'Erreurs avec Escalation**

```typescript
// Exemple : Gestion d'erreurs avec options de récupération
export async function robustOperation() {
  let retryCount = 0;
  const maxRetries = 3;

  while (retryCount < maxRetries) {
    try {
      await performRiskyOperation();

      // Succès
      await toastSuccess('Operation completed successfully');
      return;
    } catch (error) {
      retryCount++;

      if (retryCount < maxRetries) {
        // Proposer un retry via dialog
        const shouldRetry = await new Promise<boolean>((resolve) => {
          DialogManager.add({
            title: 'Operation Failed',
            description: `Attempt ${retryCount} failed. Would you like to try again?`,
            style: 'warning',
            action: {
              label: `Retry (${maxRetries - retryCount} attempts left)`,
              onClick: () => resolve(true)
            },
            cancel: {
              label: 'Cancel',
              onClick: () => resolve(false)
            }
          });
        });

        if (!shouldRetry) {
          await toastInfo('Operation cancelled by user');
          return;
        }

        // Toast de retry
        await toastInfo(`Retrying... (Attempt ${retryCount + 1}/${maxRetries})`);
      } else {
        // Échec final avec options de support
        await toastError('Operation failed after multiple attempts', {
          description: 'Please contact support if the problem persists',
          duration: 8000,
          action: {
            label: 'Contact Support',
            onClick: 'window.open("/support", "_blank")'
          }
        });
        return;
      }
    }
  }
}
```

## 📋 Bonnes Pratiques d'Intégration

### **Timing et Coordination**
1. **Dialog d'abord** : Toujours confirmer avant les actions destructives
2. **Toast immédiat** : Feedback client pour les interactions rapides
3. **Toast persistant** : Feedback serveur pour les opérations importantes
4. **Nettoyage** : Supprimer les toasts de loading après completion

### **Cohérence des Messages**
1. **Terminologie uniforme** : Utiliser les mêmes termes dans dialogs et toasts
2. **Ton cohérent** : Maintenir le même niveau de formalité
3. **Traductions synchronisées** : Assurer la cohérence entre langues

### **Gestion des États**
1. **Loading states** : Toujours indiquer les opérations en cours
2. **Error recovery** : Proposer des actions de récupération
3. **Success confirmation** : Confirmer les actions importantes

### **Performance**
1. **Lazy loading** : Charger les composants à la demande
2. **Debouncing** : Éviter les notifications en rafale
3. **Cleanup** : Nettoyer les toasts et dialogs inutilisés

## 🧪 Testing des Intégrations

### **Tests d'Intégration Dialog + Toast**

```typescript
// Exemple de test d'intégration
describe('Dialog Toast Integration', () => {
  it('should show confirmation dialog then success toast', async () => {
    const user = userEvent.setup();

    render(<AccountCard account={mockAccount} />);

    // Cliquer sur delete
    await user.click(screen.getByText('Delete Account'));

    // Vérifier que le dialog apparaît
    expect(screen.getByText('Delete @test_account')).toBeInTheDocument();

    // Confirmer la suppression
    const confirmInput = screen.getByPlaceholderText('@test_account');
    await user.type(confirmInput, '@test_account');
    await user.click(screen.getByText('Delete'));

    // Vérifier que le toast de succès apparaît
    await waitFor(() => {
      expect(screen.getByText('Account deleted successfully')).toBeInTheDocument();
    });
  });
});
```

## 📚 Ressources et Références

- **[Dialog Manager System](./dialog-manager-system.md)** - Documentation complète du système de dialogs
- **[Toast Manager System](./toast-manager-system.md)** - Documentation complète du système de toasts
- **[Component Architecture](./component-architecture.md)** - Architecture générale des composants
- **[Testing Patterns](../testing/testing-patterns.md)** - Stratégies de test pour les intégrations

---

**Cette intégration Dialog + Toast crée une expérience utilisateur fluide et cohérente, guidant l'utilisateur à travers les actions importantes avec des confirmations appropriées et un feedback clair.**
