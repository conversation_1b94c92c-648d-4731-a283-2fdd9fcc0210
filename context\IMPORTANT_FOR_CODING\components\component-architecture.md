# ⚛️ Component Architecture

This guide outlines the component architecture for the Hexa TikPay project, focusing on the distinction between server and client components in Next.js.

## 📋 Overview

The Hexa TikPay project uses Next.js App Router and follows these component principles:
- **Server-first approach**: Use server components by default
- **Strategic client components**: Only use client components when necessary
- **Clear separation of concerns**: Components should have a single responsibility
- **Type safety**: Strong TypeScript types for all components
- **Accessibility**: WCAG compliance for all UI components

## 🏗️ Component Types

### Server Components

Server components are the default in Next.js App Router. They:
- Render on the server
- Don't include client-side JavaScript
- Can't use hooks or browser APIs
- Can directly access backend resources
- Are ideal for static or data-fetching components

### Client Components

Client components are used for interactive UI elements. They:
- Include JavaScript that runs in the browser
- Can use React hooks and browser APIs
- Can't directly access backend resources
- Are marked with the `'use client'` directive
- Are ideal for interactive UI elements

## 📂 Component Organization

```
components/
├── ui/                 # Base UI components
│   ├── button.tsx      # Button component
│   ├── input.tsx       # Input component
│   └── ...             # Other UI components
├── forms/              # Form components
│   ├── login-form.tsx  # Login form
│   ├── signup-form.tsx # Signup form
│   └── ...             # Other form components
├── layout/             # Layout components
│   ├── header.tsx      # Header component
│   ├── footer.tsx      # Footer component
│   └── ...             # Other layout components
├── dashboard/          # Dashboard-specific components
│   ├── stats-card.tsx  # Stats card component
│   ├── data-table.tsx  # Data table component
│   └── ...             # Other dashboard components
└── shared/             # Shared components
    ├── error-boundary.tsx # Error boundary component
    ├── loading-spinner.tsx # Loading spinner
    └── ...             # Other shared components
```

## 🔄 Server vs Client Components

### ✅ When to Use Server Components

Server components are ideal for:

1. **Data fetching**
```tsx
import { UserList } from '@/components/users/user-list';
// app/users/page.tsx
import { db } from '@/lib/db';
import { users } from '@/lib/db/schema';

export default async function UsersPage() {
  // Data fetching directly in the server component
  const allUsers = await db.query.users.findMany({
    orderBy: { createdAt: 'desc' },
  });

  return (
    <div className="container py-8">
      <h1 className="text-2xl font-bold mb-6">Users</h1>
      <UserList users={allUsers} />
    </div>
  );
}
```

2. **Static content rendering**
```tsx
// components/dashboard/welcome-banner.tsx
import { getGreeting } from '@/lib/utils';

export function WelcomeBanner({ username }: { username: string }) {
  const greeting = getGreeting();

  return (
    <div className="bg-primary/10 p-6 rounded-lg mb-6">
      <h2 className="text-xl font-semibold">
        {greeting}
        ,
        {username}
        !
      </h2>
      <p className="mt-2">
        Welcome to your Hexa TikPay dashboard.
      </p>
    </div>
  );
}
```

3. **Layout components**
```tsx
import { Header } from '@/components/dashboard/header';
// app/dashboard/layout.tsx
import { Sidebar } from '@/components/dashboard/sidebar';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex min-h-screen">
      <Sidebar />
      <div className="flex-1">
        <Header />
        <main className="p-6">{children}</main>
      </div>
    </div>
  );
}
```

### ✅ When to Use Client Components

Client components are necessary for:

1. **Interactivity with hooks**
```tsx
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';

export function Counter() {
  const [count, setCount] = useState(0);

  return (
    <div className="flex flex-col items-center gap-4">
      <p className="text-2xl font-bold">{count}</p>
      <div className="flex gap-2">
        <Button onClick={() => setCount(count - 1)}>Decrease</Button>
        <Button onClick={() => setCount(count + 1)}>Increase</Button>
      </div>
    </div>
  );
}
```

2. **Event handling**
```tsx
'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';

export function NotificationButton() {
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('Notification sent!');
    } catch (error) {
      toast.error('Failed to send notification');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button onClick={handleClick} disabled={isLoading}>
      {isLoading ? 'Sending...' : 'Send Notification'}
    </Button>
  );
}
```

3. **Browser API usage**
```tsx
'use client';

import { useEffect, useState } from 'react';

export function GeolocationDisplay() {
  const [location, setLocation] = useState<GeolocationCoordinates | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported by your browser');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      position => setLocation(position.coords),
      error => setError(error.message)
    );
  }, []);

  if (error) {
    return (
      <div className="text-red-500">
        Error:
        {error}
      </div>
    );
  }

  if (!location) {
    return <div>Loading location...</div>;
  }

  return (
    <div>
      <p>
        Latitude:
        {location.latitude}
      </p>
      <p>
        Longitude:
        {location.longitude}
      </p>
    </div>
  );
}
```

## 🔀 Component Patterns

### 1. Server Component with Client Component Children

```tsx
import { redirect, useRouter } from 'next/navigation';
// app/dashboard/page.tsx (Server Component)
import { Suspense } from 'react';
import { ActivityFeed } from '@/components/dashboard/activity-feed';
import { DashboardStats } from '@/components/dashboard/dashboard-stats';
import { RefreshButton } from '@/components/dashboard/refresh-button';
import { Button } from '@/components/ui/button';
import { RefreshIcon } from '@/components/ui/icons';
import { LoadingSkeleton } from '@/components/ui/loading-skeleton';

import { auth } from '@/lib/auth';
import { db } from '@/lib/db';

export default async function DashboardPage() {
  const session = await auth();
  if (!session) {
    redirect('/login');
  }

  const stats = await db.query.dashboardStats.findFirst({
    where: { userId: session.user.id },
  });

  const activities = await db.query.activities.findMany({
    where: { userId: session.user.id },
    orderBy: { createdAt: 'desc' },
    limit: 10,
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Dashboard</h1>
        {/* Client component for refresh functionality */}
        <RefreshButton />
      </div>

      <DashboardStats stats={stats} />

      <Suspense fallback={<LoadingSkeleton />}>
        <ActivityFeed activities={activities} />
      </Suspense>
    </div>
  );
}

// components/dashboard/refresh-button.tsx (Client Component)
'use client';

export function RefreshButton() {
  const router = useRouter();

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={() => router.refresh()}
    >
      <RefreshIcon className="mr-2 h-4 w-4" />
      Refresh
    </Button>
  );
}
```

### 2. Client Component Boundary

```tsx
import { useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';

// components/accounts/account-item.tsx (Server Component)
import { formatCurrency } from '@/lib/utils';
import { AccountActions } from './account-actions';
// components/accounts/account-list.tsx (Server Component)
import { AccountItem } from './account-item';

export function AccountList({ accounts }) {
  return (
    <div className="space-y-4">
      {accounts.map(account => (
        <div key={account.id} className="border rounded-lg p-4">
          {/* Server component for displaying data */}
          <AccountItem account={account} />

          {/* Client component boundary for interactive elements */}
          <AccountActions account={account} />
        </div>
      ))}
    </div>
  );
}

export function AccountItem({ account }) {
  return (
    <div>
      <h3 className="text-lg font-medium">{account.name}</h3>
      <p className="text-gray-500">{account.number}</p>
      <p className="text-xl font-bold mt-2">
        {formatCurrency(account.balance)}
      </p>
    </div>
  );
}

// components/accounts/account-actions.tsx (Client Component)
'use client';

export function AccountActions({ account }) {
  const [isLoading, setIsLoading] = useState(false);

  const handleDeactivate = async () => {
    if (!confirm(`Are you sure you want to deactivate ${account.name}?`)) {
      return;
    }

    setIsLoading(true);
    try {
      await fetch(`/api/accounts/${account.id}/deactivate`, {
        method: 'POST',
      });
      toast.success('Account deactivated');
    } catch (error) {
      toast.error('Failed to deactivate account');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mt-4 flex gap-2">
      <Button variant="outline" size="sm">View Details</Button>
      <Button
        variant="destructive"
        size="sm"
        onClick={handleDeactivate}
        disabled={isLoading}
      >
        {isLoading ? 'Processing...' : 'Deactivate'}
      </Button>
    </div>
  );
}
```

### 3. Props Drilling Alternative

Instead of passing props through multiple levels, use:

1. **Context API for Client Components**
```tsx
'use client';

import { createContext, use, useState } from 'react';

import { useTheme } from '@/components/theme-provider';
import { Button } from '@/components/ui/button';

// Create context
const ThemeContext = createContext({
  theme: 'light',
  toggleTheme: () => {},
});

// Provider component
export function ThemeProvider({ children }) {
  const [theme, setTheme] = useState('light');

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  return (
    <ThemeContext value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext>
  );
}

// Hook to use the context
export function useTheme() {
  return use(ThemeContext);
}

// Usage in a client component
'use client';

export function ThemeToggle() {
  const { theme, toggleTheme } = useTheme();

  return (
    <Button onClick={toggleTheme}>
      {theme === 'light' ? 'Dark Mode' : 'Light Mode'}
    </Button>
  );
}
```

2. **Server Component Composition**
```tsx
// Parent server component passes data to children
export default async function SettingsPage() {
  const user = await getCurrentUser();
  const preferences = await getUserPreferences(user.id);

  return (
    <div>
      <h1>Settings</h1>

      {/* Each section gets only what it needs */}
      <ProfileSection user={user} />
      <NotificationSection preferences={preferences.notifications} />
      <SecuritySection
        twoFactorEnabled={preferences.security.twoFactorEnabled}
        userId={user.id}
      />
    </div>
  );
}
```

## 📦 Composable Components

### 1. Compound Components

```tsx
'use client';

import { createContext, use, useState } from 'react';

// Create context
const TabsContext = createContext(null);

// Main component
export function Tabs({ defaultValue, children }) {
  const [activeTab, setActiveTab] = useState(defaultValue);

  return (
    <TabsContext value={{ activeTab, setActiveTab }}>
      <div className="tabs">{children}</div>
    </TabsContext>
  );
}

// List component
Tabs.List = function TabsList({ children }) {
  return (
    <div className="tabs-list border-b flex gap-2">
      {children}
    </div>
  );
};

// Trigger component
Tabs.Trigger = function TabsTrigger({ value, children }) {
  const { activeTab, setActiveTab } = use(TabsContext);

  return (
    <button
      className={`px-4 py-2 ${activeTab === value ? 'border-b-2 border-primary' : ''}`}
      onClick={() => setActiveTab(value)}
    >
      {children}
    </button>
  );
};

// Content component
Tabs.Content = function TabsContent({ value, children }) {
  const { activeTab } = use(TabsContext);

  if (activeTab !== value) {
    return null;
  }

  return <div className="p-4">{children}</div>;
};

// Usage
export function ProfileTabs() {
  return (
    <Tabs defaultValue="personal">
      <Tabs.List>
        <Tabs.Trigger value="personal">Personal Info</Tabs.Trigger>
        <Tabs.Trigger value="billing">Billing</Tabs.Trigger>
        <Tabs.Trigger value="security">Security</Tabs.Trigger>
      </Tabs.List>
      <Tabs.Content value="personal">
        <PersonalInfoForm />
      </Tabs.Content>
      <Tabs.Content value="billing">
        <BillingDetailsForm />
      </Tabs.Content>
      <Tabs.Content value="security">
        <SecuritySettings />
      </Tabs.Content>
    </Tabs>
  );
}
```

### 2. Higher-Order Components

```tsx
'use client';

import { useEffect, useState } from 'react';

// Higher-order component for loading state
export function withLoading(Component) {
  return function WithLoadingComponent({ isLoading, ...props }) {
    if (isLoading) {
      return (
        <div className="flex justify-center items-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        </div>
      );
    }

    return <Component {...props} />;
  };
}

// Usage
function UserList({ users }) {
  return (
    <ul>
      {users.map(user => (
        <li key={user.id}>{user.name}</li>
      ))}
    </ul>
  );
}

const UserListWithLoading = withLoading(UserList);

export function UsersContainer() {
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchUsers() {
      try {
        const response = await fetch('/api/users');
        const data = await response.json();
        setUsers(data.users);
      } catch (error) {
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchUsers();
  }, []);

  return <UserListWithLoading isLoading={isLoading} users={users} />;
}
```

## 🧩 Component Props Best Practices

### 1. Type Definition

```tsx
// Define props interface
type UserCardProps = {
  user: {
    id: string;
    name: string;
    email: string;
    role: 'admin' | 'user';
    avatarUrl?: string;
  };
  isSelected?: boolean;
  onSelect?: (userId: string) => void;
};

// Use the interface
export function UserCard({
  user,
  isSelected = false,
  onSelect,
}: UserCardProps) {
  // Component implementation
}
```

### 2. Default Props

```tsx
type PaginationProps = {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  siblingCount?: number;
};

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  siblingCount = 1,
}: PaginationProps) {
  // Component implementation
}
```

### 3. Component Variants

```tsx
// Define variants
type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'link';
type ButtonSize = 'sm' | 'md' | 'lg';

type ButtonProps = {
  variant?: ButtonVariant;
  size?: ButtonSize;
  isLoading?: boolean;
} & React.ButtonHTMLAttributes<HTMLButtonElement>;

// Apply variants using classes
export function Button({
  variant = 'primary',
  size = 'md',
  isLoading = false,
  className,
  children,
  ...props
}: ButtonProps) {
  // Get classes based on variant and size
  const variantClasses = {
    primary: 'bg-primary text-white hover:bg-primary/90',
    secondary: 'bg-secondary text-white hover:bg-secondary/90',
    outline: 'border border-gray-200 hover:bg-gray-100',
    ghost: 'hover:bg-gray-100',
    link: 'text-primary underline hover:text-primary/90',
  };

  const sizeClasses = {
    sm: 'px-3 py-1 text-sm',
    md: 'px-4 py-2',
    lg: 'px-6 py-3 text-lg',
  };

  return (
    <button
      className={`
        rounded-md font-medium transition-colors
        focus:outline-none focus:ring-2 focus:ring-primary/50
        disabled:opacity-50 disabled:pointer-events-none
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${className}
      `}
      disabled={isLoading || props.disabled}
      {...props}
    >
      {isLoading
        ? (
            <span className="flex items-center gap-2">
              <span className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full"></span>
              Loading...
            </span>
          )
        : (
            children
          )}
    </button>
  );
}
```

## 🧪 Component Testing

### 1. Server Component Testing

```tsx
// __tests__/components/user-list.test.tsx
import { render, screen } from '@testing-library/react';
import { UserList } from '@/components/users/user-list';

describe('UserList', () => {
  const mockUsers = [
    { id: '1', name: 'John Doe', email: '<EMAIL>' },
    { id: '2', name: 'Jane Smith', email: '<EMAIL>' },
  ];

  it('renders users correctly', () => {
    render(<UserList users={mockUsers} />);

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('shows no users message when list is empty', () => {
    render(<UserList users={[]} />);

    expect(screen.getByText('No users found')).toBeInTheDocument();
  });
});
```

### 2. Client Component Testing

```tsx
// __tests__/components/counter.test.tsx
import { fireEvent, render, screen } from '@testing-library/react';
import { Counter } from '@/components/counter';

describe('Counter', () => {
  it('renders initial count', () => {
    render(<Counter />);

    expect(screen.getByText('0')).toBeInTheDocument();
  });

  it('increments count when increase button is clicked', () => {
    render(<Counter />);

    fireEvent.click(screen.getByText('Increase'));

    expect(screen.getByText('1')).toBeInTheDocument();
  });

  it('decrements count when decrease button is clicked', () => {
    render(<Counter />);

    fireEvent.click(screen.getByText('Increase')); // First increase to 1
    fireEvent.click(screen.getByText('Decrease')); // Then decrease back to 0

    expect(screen.getByText('0')).toBeInTheDocument();
  });
});
```

## 📋 Component Best Practices Checklist

- [ ] Use server components by default for non-interactive UI
- [ ] Only use client components when interactivity is needed
- [ ] Keep client component bundles small and focused
- [ ] Use compound components for complex UI patterns
- [ ] Apply proper TypeScript typing for all components
- [ ] Use default props for optional properties
- [ ] Implement proper error boundaries
- [ ] Handle loading states for async operations
- [ ] Ensure components are accessible
- [ ] Write tests for critical component functionality
- [ ] Use meaningful component names
- [ ] Minimize prop drilling with context or composition
- [ ] Avoid large client component trees
- [ ] Extract reusable logic into custom hooks
- [ ] Use Suspense for loading states when appropriate

## 📚 Cross-References

- **[Form Handling](./form-handling.md)** - Form component patterns and validation
- **[Dialog Manager System](./dialog-manager-system.md)** - Modal and confirmation dialogs
- **[Toast Manager System](./toast-manager-system.md)** - Server-to-client notifications
- **[Validation Patterns](../validation/validation-patterns.md)** - Input validation and type safety
- **[Testing Patterns](../testing/testing-patterns.md)** - Component testing strategies
- **[API Patterns](../api/api-patterns.md)** - Data fetching and server integration

---

**This component architecture ensures scalable, maintainable, and performant React components throughout the Hexa TikPay application.**
