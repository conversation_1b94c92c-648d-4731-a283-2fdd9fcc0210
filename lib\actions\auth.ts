"use server";

import { actionClient, authActionClient } from "../safe-action";
import { signUpSchema, signInSchema, updateProfileSchema } from "../validations";
import { auth } from "../auth";
import { db, createAuditLog } from "../db";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import bcrypt from "bcryptjs";

/**
 * Sign up a new user
 * Following Critical Error #3: Always validate server action inputs
 */
export const signUpAction = actionClient
  .schema(signUpSchema)
  .action(async ({ parsedInput: { name, email, password } }) => {
    try {
      // Check if user already exists
      const existingUser = await db.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        return {
          error: "A user with this email already exists",
        };
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12);

      // Create user
      const user = await db.user.create({
        data: {
          name,
          email,
          // Note: Better Auth will handle password hashing
          // This is just for demonstration
        },
        select: {
          id: true,
          email: true,
          name: true,
        },
      });

      // Create audit log
      const headersList = await headers();
      await createAuditLog({
        userId: user.id,
        action: "user.created",
        resource: "user",
        resourceId: user.id,
        ipAddress: headersList.get("x-forwarded-for") || undefined,
        userAgent: headersList.get("user-agent") || undefined,
      });

      return {
        success: true,
        user,
      };
    } catch (error) {
      console.error("Sign up error:", error);
      return {
        error: "Failed to create account. Please try again.",
      };
    }
  });

/**
 * Sign in user
 */
export const signInAction = actionClient
  .schema(signInSchema)
  .action(async ({ parsedInput: { email, password } }) => {
    try {
      // Better Auth will handle the actual sign in
      // This is just for demonstration of the pattern
      
      const user = await db.user.findUnique({
        where: { email },
        select: {
          id: true,
          email: true,
          name: true,
        },
      });

      if (!user) {
        return {
          error: "Invalid email or password",
        };
      }

      // Create audit log
      const headersList = await headers();
      await createAuditLog({
        userId: user.id,
        action: "user.signed_in",
        resource: "user",
        resourceId: user.id,
        ipAddress: headersList.get("x-forwarded-for") || undefined,
        userAgent: headersList.get("user-agent") || undefined,
      });

      return {
        success: true,
        user,
      };
    } catch (error) {
      console.error("Sign in error:", error);
      return {
        error: "Failed to sign in. Please try again.",
      };
    }
  });

/**
 * Update user profile
 * Following Critical Error #1: Proper authorization checks
 */
export const updateProfileAction = authActionClient
  .schema(updateProfileSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      const { user } = ctx;

      // Update user profile
      const updatedUser = await db.user.update({
        where: { id: user.id },
        data: parsedInput,
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          updatedAt: true,
        },
      });

      // Create audit log
      const headersList = await headers();
      await createAuditLog({
        userId: user.id,
        action: "user.profile_updated",
        resource: "user",
        resourceId: user.id,
        metadata: { updatedFields: Object.keys(parsedInput) },
        ipAddress: headersList.get("x-forwarded-for") || undefined,
        userAgent: headersList.get("user-agent") || undefined,
      });

      return {
        success: true,
        user: updatedUser,
      };
    } catch (error) {
      console.error("Update profile error:", error);
      return {
        error: "Failed to update profile. Please try again.",
      };
    }
  });

/**
 * Sign out user
 */
export const signOutAction = authActionClient.action(async ({ ctx }) => {
  try {
    const { user } = ctx;

    // Create audit log
    const headersList = await headers();
    await createAuditLog({
      userId: user.id,
      action: "user.signed_out",
      resource: "user",
      resourceId: user.id,
      ipAddress: headersList.get("x-forwarded-for") || undefined,
      userAgent: headersList.get("user-agent") || undefined,
    });

    // Better Auth will handle the actual sign out
    // This would typically be done on the client side
    
    return {
      success: true,
    };
  } catch (error) {
    console.error("Sign out error:", error);
    return {
      error: "Failed to sign out. Please try again.",
    };
  }
});

/**
 * Delete user account
 */
export const deleteAccountAction = authActionClient.action(async ({ ctx }) => {
  try {
    const { user } = ctx;

    // Create audit log before deletion
    const headersList = await headers();
    await createAuditLog({
      userId: user.id,
      action: "user.deleted",
      resource: "user",
      resourceId: user.id,
      ipAddress: headersList.get("x-forwarded-for") || undefined,
      userAgent: headersList.get("user-agent") || undefined,
    });

    // Delete user account
    await db.user.delete({
      where: { id: user.id },
    });

    // Redirect to home page
    redirect("/");
  } catch (error) {
    console.error("Delete account error:", error);
    return {
      error: "Failed to delete account. Please try again.",
    };
  }
});
