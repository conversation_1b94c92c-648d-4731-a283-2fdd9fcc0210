"use client";

import { motion } from "framer-motion";
import { ReactNode } from "react";
import { useScrollAnimation, useParallaxBackground } from "@/hooks/use-scroll-animation";
import { cn } from "@/lib/utils";

interface ParallaxWrapperProps {
  children: ReactNode;
  className?: string;
  parallaxRange?: [number, number];
  animationType?: "fade" | "slide" | "scale" | "rotate" | "parallax" | "none";
  delay?: number;
  duration?: number;
  triggerOnce?: boolean;
  threshold?: number;
  springConfig?: {
    stiffness: number;
    damping: number;
    mass: number;
  };
}

export function ParallaxWrapper({
  children,
  className,
  parallaxRange = [-30, 30],
  animationType = "fade",
  delay = 0,
  duration = 0.6,
  triggerOnce = true,
  threshold = 0.1,
  springConfig = { stiffness: 100, damping: 30, mass: 1 }
}: ParallaxWrapperProps) {
  const { ref, isInView, parallaxY } = useScrollAnimation({
    threshold,
    triggerOnce,
    parallaxRange,
    springConfig
  });

  // Animation variants based on type
  const getAnimationProps = () => {
    const baseTransition = {
      duration,
      delay,
      ease: [0.25, 0.46, 0.45, 0.94] // Custom easing for smooth animations
    };

    switch (animationType) {
      case "fade":
        return {
          initial: { opacity: 0 },
          animate: isInView ? { opacity: 1 } : { opacity: 0 },
          transition: baseTransition
        };

      case "slide":
        return {
          initial: { opacity: 0, y: 50 },
          animate: isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 },
          transition: baseTransition
        };

      case "scale":
        return {
          initial: { opacity: 0, scale: 0.8 },
          animate: isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 },
          transition: baseTransition
        };

      case "rotate":
        return {
          initial: { opacity: 0, rotate: -10 },
          animate: isInView ? { opacity: 1, rotate: 0 } : { opacity: 0, rotate: -10 },
          transition: baseTransition
        };

      case "parallax":
        return {
          style: { y: parallaxY },
          initial: { opacity: 0 },
          animate: isInView ? { opacity: 1 } : { opacity: 0 },
          transition: baseTransition
        };

      default:
        return {};
    }
  };

  return (
    <motion.div
      ref={ref}
      className={cn("will-change-transform", className)}
      {...getAnimationProps()}
    >
      {children}
    </motion.div>
  );
}

// Specialized components for different effects
export function FadeInWrapper({ children, className, delay = 0, ...props }: Omit<ParallaxWrapperProps, 'animationType'>) {
  return (
    <ParallaxWrapper
      animationType="fade"
      className={className}
      delay={delay}
      {...props}
    >
      {children}
    </ParallaxWrapper>
  );
}

export function SlideInWrapper({ children, className, delay = 0, ...props }: Omit<ParallaxWrapperProps, 'animationType'>) {
  return (
    <ParallaxWrapper
      animationType="slide"
      className={className}
      delay={delay}
      {...props}
    >
      {children}
    </ParallaxWrapper>
  );
}

export function ScaleInWrapper({ children, className, delay = 0, ...props }: Omit<ParallaxWrapperProps, 'animationType'>) {
  return (
    <ParallaxWrapper
      animationType="scale"
      className={className}
      delay={delay}
      {...props}
    >
      {children}
    </ParallaxWrapper>
  );
}

export function ParallaxBackground({
  children,
  className,
  speed = 0.5
}: {
  children: ReactNode;
  className?: string;
  speed?: number;
}) {
  const { y } = useParallaxBackground(speed);

  return (
    <motion.div
      className={cn("absolute inset-0 will-change-transform", className)}
      style={{ y }}
    >
      {children}
    </motion.div>
  );
}

// Staggered animation wrapper for lists
export function StaggeredWrapper({
  children,
  className,
  staggerDelay = 0.1,
  itemClassName = ""
}: {
  children: ReactNode[];
  className?: string;
  staggerDelay?: number;
  itemClassName?: string;
}) {
  return (
    <div className={className}>
      {children.map((child, index) => (
        <SlideInWrapper
          key={index}
          delay={index * staggerDelay}
          className={itemClassName}
        >
          {child}
        </SlideInWrapper>
      ))}
    </div>
  );
}
