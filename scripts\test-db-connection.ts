#!/usr/bin/env tsx

/**
 * Test script to verify Supabase + Prisma integration
 * This script tests:
 * 1. Database connection
 * 2. Basic CRUD operations
 * 3. Cached query functions
 * 4. Multi-tenant organization system
 */

import { db, getUserById, getUserByEmail, createAuditLog } from "../lib/db";
import { env } from "../lib/env";

async function testDatabaseConnection() {
  console.log("🔍 Testing Supabase + Prisma Integration...\n");

  try {
    // Test 1: Basic connection
    console.log("1️⃣ Testing database connection...");
    const result = await db.$queryRaw`SELECT version()`;
    console.log("✅ Database connected successfully");
    console.log("📊 PostgreSQL version:", result);
    console.log("");

    // Test 2: Create a test user
    console.log("2️⃣ Creating test user...");
    const testUser = await db.user.create({
      data: {
        email: "<EMAIL>",
        name: "Test User",
        emailVerified: true,
      },
    });
    console.log("✅ Test user created:", testUser);
    console.log("");

    // Test 3: Test cached query functions
    console.log("3️⃣ Testing cached query functions...");
    
    // Test getUserById (cached)
    const userById = await getUserById(testUser.id);
    console.log("✅ getUserById (cached):", userById);
    
    // Test getUserByEmail (cached)
    const userByEmail = await getUserByEmail(testUser.email);
    console.log("✅ getUserByEmail (cached):", userByEmail);
    console.log("");

    // Test 4: Create test organization
    console.log("4️⃣ Creating test organization...");
    const testOrg = await db.organization.create({
      data: {
        name: "Test Organization",
        slug: "test-org",
        description: "A test organization for our boilerplate",
      },
    });
    console.log("✅ Test organization created:", testOrg);
    console.log("");

    // Test 5: Add user to organization
    console.log("5️⃣ Adding user to organization...");
    const membership = await db.organizationMember.create({
      data: {
        organizationId: testOrg.id,
        userId: testUser.id,
        role: "OWNER",
      },
    });
    console.log("✅ Organization membership created:", membership);
    console.log("");

    // Test 6: Test audit logging
    console.log("6️⃣ Testing audit logging...");
    await createAuditLog({
      organizationId: testOrg.id,
      userId: testUser.id,
      action: "test.database_connection",
      resource: "database",
      resourceId: "test",
      metadata: {
        testType: "integration",
        timestamp: new Date().toISOString(),
      },
      ipAddress: "127.0.0.1",
      userAgent: "test-script",
    });
    console.log("✅ Audit log created successfully");
    console.log("");

    // Test 7: Query organization with members
    console.log("7️⃣ Testing complex queries...");
    const orgWithMembers = await db.organization.findUnique({
      where: { id: testOrg.id },
      include: {
        members: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });
    console.log("✅ Organization with members:", JSON.stringify(orgWithMembers, null, 2));
    console.log("");

    // Test 8: Test environment variables
    console.log("8️⃣ Testing environment configuration...");
    console.log("✅ Database URL configured:", !!env.DATABASE_URL);
    console.log("✅ Direct URL configured:", !!env.DIRECT_URL);
    console.log("✅ Supabase URL configured:", !!env.NEXT_PUBLIC_SUPABASE_URL);
    console.log("✅ Supabase Anon Key configured:", !!env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
    console.log("");

    // Cleanup: Remove test data
    console.log("🧹 Cleaning up test data...");
    await db.organizationMember.delete({ where: { id: membership.id } });
    await db.organization.delete({ where: { id: testOrg.id } });
    await db.user.delete({ where: { id: testUser.id } });
    console.log("✅ Test data cleaned up");
    console.log("");

    console.log("🎉 All tests passed! Supabase + Prisma integration is working correctly.");
    console.log("");
    console.log("📋 Summary:");
    console.log("  ✅ Database connection established");
    console.log("  ✅ CRUD operations working");
    console.log("  ✅ Cached queries functioning");
    console.log("  ✅ Multi-tenant system operational");
    console.log("  ✅ Audit logging working");
    console.log("  ✅ Environment variables validated");
    console.log("");
    console.log("🚀 Ready to build authentication pages and dashboard!");

  } catch (error) {
    console.error("❌ Database test failed:", error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// Run the test
testDatabaseConnection();
