"use client";


import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Play, CheckCircle, Star } from "lucide-react";
import { heroStats, companyLogos } from "@/lib/constants";
import { FadeInWrapper, SlideInWrapper, ParallaxBackground, StaggeredWrapper } from "@/components/ui/parallax-wrapper";

export function HeroSection() {

  return (
    <section className="relative mt-16 min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background via-background to-muted/20">
      {/* Background Elements with Parallax */}
      <ParallaxBackground speed={0.2}>
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]" />
      </ParallaxBackground>

      <div className="absolute inset-0 bg-gradient-to-t from-background via-transparent to-transparent" />

      {/* Floating Elements with Parallax */}
      <ParallaxBackground speed={0.3}>
        <div className="absolute top-20 left-10 w-20 h-20 bg-primary/10 rounded-full blur-xl animate-pulse" />
      </ParallaxBackground>

      <ParallaxBackground speed={0.4}>
        <div className="absolute top-40 right-20 w-32 h-32 bg-blue-500/10 rounded-full blur-xl animate-pulse delay-1000" />
      </ParallaxBackground>

      <ParallaxBackground speed={0.5}>
        <div className="absolute bottom-20 left-20 w-24 h-24 bg-purple-500/10 rounded-full blur-xl animate-pulse delay-2000" />
      </ParallaxBackground>

      <div className="section-container">
        <div className="section-content-wide text-center">
          {/* Announcement Badge */}
          <FadeInWrapper delay={0} className="mb-8">
            <Badge variant="outline" className="px-4 py-2 text-sm bg-background/50 backdrop-blur-sm border-primary/20">
              <Star className="w-4 h-4 mr-2 text-yellow-500" />
              10,000+ TikTok accounts sold to influencers worldwide
            </Badge>
          </FadeInWrapper>

          {/* Main Heading */}
          <SlideInWrapper delay={0.2} className="mb-12">
            <h1 className="heading-hero mb-8">
              <span className="bg-gradient-to-r from-foreground via-foreground to-primary bg-clip-text text-transparent">
                Pre-Monetized TikTok
              </span>
              <br />
              <span className="bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
                Accounts Marketplace
              </span>
            </h1>
            <p className="text-lead min-w-4xl mx-auto">
              Skip the grind and start with established TikTok accounts. Get instant access to engaged audiences,
              proven content strategies, and monetization opportunities.
            </p>
          </SlideInWrapper>

          {/* CTA Buttons */}
          <FadeInWrapper delay={0.4} className="mb-12">
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button size="lg" className="px-8 py-6 text-lg font-semibold transition-all hover:scale-105" asChild>
                <Link href="/auth/signup">
                  Browse Accounts
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform hover:translate-x-1" />
                </Link>
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="px-8 py-6 text-lg font-semibold bg-background/50 backdrop-blur-sm border-primary/20 hover:bg-primary/5 transition-all hover:scale-105"
              >
                <Play className="mr-2 h-5 w-5" />
                See Success Stories
              </Button>
            </div>
          </FadeInWrapper>

          {/* Stats */}
          <div className="mb-16">
            <StaggeredWrapper
              className="grid-4-cols section-content-narrow"
              staggerDelay={0.1}
              itemClassName="text-center"
            >
              {heroStats.map((stat, index) => (
                <div key={index}>
                  <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-primary mb-2 transition-transform hover:scale-110">
                    {stat.value}
                  </div>
                  <div className="text-sm md:text-base text-muted-foreground">
                    {stat.label}
                  </div>
                </div>
              ))}
            </StaggeredWrapper>
          </div>

          {/* Social Proof */}
          <FadeInWrapper delay={0.8} className="mb-12">
            <p className="text-sm text-muted-foreground mb-8">
              Trusted by top influencers and brands worldwide
            </p>
            <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
              {companyLogos.map((logo, index) => (
                <div
                  key={index}
                  className="text-lg font-semibold text-muted-foreground hover:text-foreground transition-colors cursor-pointer"
                >
                  {logo}
                </div>
              ))}
            </div>
          </FadeInWrapper>

          {/* Trust Indicators */}
          <FadeInWrapper delay={1.0}>
            <div className="flex flex-wrap justify-center items-center gap-6 md:gap-8 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Verified Accounts</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Secure Transfer</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Quality Guarantee</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>24/7 Support</span>
              </div>
            </div>
          </FadeInWrapper>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-muted-foreground/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-muted-foreground/30 rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </section>
  );
}
