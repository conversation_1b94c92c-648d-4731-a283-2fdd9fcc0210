"use client";

import { createAuthClient } from "better-auth/react";
import { organizationClient } from "better-auth/client/plugins";
import { env } from "./env";

export const authClient = createAuthClient({
  baseURL: env.NEXT_PUBLIC_APP_URL,
  plugins: [organizationClient()],
});

export const {
  signIn,
  signUp,
  signOut,
  useSession,
  getSession,
  organization,
} = authClient;

// Custom hooks for better DX
export function useUser() {
  const { data: session } = useSession();
  return session?.user ?? null;
}

export function useIsAuthenticated() {
  const { data: session } = useSession();
  return !!session?.user;
}
