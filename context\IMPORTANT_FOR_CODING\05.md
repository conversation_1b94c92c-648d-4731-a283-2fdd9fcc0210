Ce "Dialog Manager" est une excellente abstraction pour simplifier la gestion des boîtes de dialogue d'alerte (et potentiellement d'autres types de modales) dans une application React/Next.js, surtout si vous utilisez une bibliothèque de composants comme Shadcn/ui (qui fournit `AlertDialog`).

**Utilité principale :**

1.  **Réduction du code répétitif (Boilerplate) :** Au lieu d'écrire tout le JSX de `AlertDialog`, `AlertDialogTrigger`, `AlertDialogContent`, etc., à chaque fois que vous avez besoin d'une confirmation, vous appelez une simple fonction.
2.  **Découplage :** Vous pouvez déclencher une boîte de dialogue depuis n'importe où dans votre code (logique métier, gestionnaires d'événements) sans avoir à passer des props ou à gérer l'état d'ouverture/fermeture localement.
3.  **Centralisation de la logique et du style :** Le style et le comportement des boîtes de dialogue sont définis en un seul endroit, ce qui facilite les modifications et assure la cohérence.
4.  **Fonctionnalités avancées faciles à ajouter :** Comme montré (confirmation par texte, champs de saisie), il est simple d'étendre les capacités de ces dialogues.

**Comment reproduire un système similaire :**

On va utiliser React, Zustand pour la gestion d'état global (comme dans la vidéo) et une bibliothèque de composants UI comme Shadcn/ui (pour `AlertDialog`).

**Étapes :**

1.  **Installation des dépendances :**
    *   `zustand`
    *   `lucide-react` (pour les icônes, souvent utilisé avec Shadcn/ui)
    *   Les composants Shadcn/ui nécessaires (`alert-dialog`, `button`, `input`, `label`).
        ```bash
        npm install zustand lucide-react
        npx shadcn-ui@latest add alert-dialog button input label sonner # sonner pour les notifications
        ```

2.  **Définir le Store Zustand (`store/dialogStore.ts`) :**
    Ce store contiendra la liste des dialogues à afficher. On n'en affichera généralement qu'un à la fois (le premier de la liste).

    ```typescript
    import { create } from 'zustand';
    import { ReactNode } from 'react';

    export interface DialogAction {
      label: string;
      onClick: (inputValue?: string) => void | Promise<void>; // inputValue pour les champs de saisie
      variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
    }

    export interface DialogInput {
      label: string;
      placeholder?: string;
      defaultValue?: string;
    }

    export interface DialogConfig {
      id: string;
      title: string;
      description?: ReactNode;
      action: DialogAction;
      cancelLabel?: string;
      onCancel?: () => void;
      confirmText?: string; // Texte à taper pour confirmer
      input?: DialogInput;   // Pour un champ de saisie simple
      customContent?: ReactNode; // Pour un contenu totalement personnalisé
      isLoading?: boolean; // Pour gérer l'état de chargement de l'action
    }

    interface DialogStoreState {
      dialogs: DialogConfig[];
      addDialog: (config: Omit<DialogConfig, 'id' | 'isLoading'>) => string;
      removeDialog: (id: string) => void;
      updateDialog: (id: string, updates: Partial<DialogConfig>) => void;
    }

    export const useDialogStore = create<DialogStoreState>((set, get) => ({
      dialogs: [],
      addDialog: (config) => {
        const id = Math.random().toString(36).substring(2, 9);
        const newDialog = { ...config, id, isLoading: false };
        set((state) => ({ dialogs: [...state.dialogs, newDialog] }));
        return id;
      },
      removeDialog: (id) => {
        set((state) => ({
          dialogs: state.dialogs.filter((dialog) => dialog.id !== id),
        }));
      },
      updateDialog: (id, updates) => {
        set((state) => ({
          dialogs: state.dialogs.map((dialog) =>
            dialog.id === id ? { ...dialog, ...updates } : dialog
          ),
        }));
      }
    }));

    // Helper pour un accès plus facile
    export const dialogManager = {
      add: (config: Omit<DialogConfig, 'id' | 'isLoading'>) => {
        return useDialogStore.getState().addDialog(config);
      },
      remove: (id: string) => {
        useDialogStore.getState().removeDialog(id);
      },
    };
    ```

3.  **Créer le composant de rendu du dialogue (`components/DialogRenderer.tsx`) :**
    Ce composant va lire le store et afficher le dialogue actif.

    ```typescript
    "use client";

    import { useState, useEffect } from 'react';
    import { useDialogStore, DialogConfig } from '@/store/dialogStore';
    import {
      AlertDialog,
      AlertDialogAction,
      AlertDialogCancel,
      AlertDialogContent,
      AlertDialogDescription,
      AlertDialogFooter,
      AlertDialogHeader,
      AlertDialogTitle,
    } from "@/components/ui/alert-dialog";
    import { Button } from "@/components/ui/button";
    import { Input } from "@/components/ui/input";
    import { Label } from "@/components/ui/label";
    import { Loader2 } from 'lucide-react';

    const DialogInstance = ({ dialog }: { dialog: DialogConfig }) => {
      const { removeDialog, updateDialog } = useDialogStore.getState();
      const [inputValue, setInputValue] = useState(dialog.input?.defaultValue || '');
      const [confirmInputValue, setConfirmInputValue] = useState('');

      const isConfirmDisabled = dialog.confirmText ? confirmInputValue !== dialog.confirmText : false;

      const handleAction = async () => {
        updateDialog(dialog.id, { isLoading: true });
        try {
          await dialog.action.onClick(dialog.input ? inputValue : undefined);
          removeDialog(dialog.id);
        } catch (error) {
          console.error("Dialog action failed:", error);
          // Optionnel: afficher une notification d'erreur (ex: avec Sonner)
          // toast.error("Quelque chose s'est mal passé.");
          updateDialog(dialog.id, { isLoading: false }); // Réinitialiser le chargement en cas d'erreur
        }
        // Ne pas appeler removeDialog ici si l'action le fait déjà ou si on veut garder le dialog ouvert
      };

      const handleCancel = () => {
        if (dialog.onCancel) {
          dialog.onCancel();
        }
        removeDialog(dialog.id);
      };

      // Permet de fermer avec la touche Echap si onCancel est défini (comportement par défaut d'AlertDialog)
      const onOpenChange = (open: boolean) => {
        if (!open) {
          handleCancel();
        }
      };

      return (
        <AlertDialog open={true} onOpenChange={onOpenChange}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>{dialog.title}</AlertDialogTitle>
              {dialog.description && (
                <AlertDialogDescription>{dialog.description}</AlertDialogDescription>
              )}
            </AlertDialogHeader>

            {dialog.customContent}

            {dialog.input && (
              <div className="grid gap-2 py-2">
                <Label htmlFor={`dialog-input-${dialog.id}`}>{dialog.input.label}</Label>
                <Input
                  id={`dialog-input-${dialog.id}`}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  placeholder={dialog.input.placeholder}
                />
              </div>
            )}

            {dialog.confirmText && (
              <div className="grid gap-2 py-2">
                <Label htmlFor={`confirm-input-${dialog.id}`}>
                  Veuillez taper "<span className="font-semibold">{dialog.confirmText}</span>" pour confirmer.
                </Label>
                <Input
                  id={`confirm-input-${dialog.id}`}
                  value={confirmInputValue}
                  onChange={(e) => setConfirmInputValue(e.target.value)}
                />
              </div>
            )}

            <AlertDialogFooter>
              <AlertDialogCancel asChild>
                <Button variant="outline" onClick={handleCancel}>
                  {dialog.cancelLabel || 'Annuler'}
                </Button>
              </AlertDialogCancel>
              <AlertDialogAction asChild>
                <Button
                  variant={dialog.action.variant || 'default'}
                  onClick={handleAction}
                  disabled={isConfirmDisabled || dialog.isLoading}
                >
                  {dialog.isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {dialog.action.label}
                </Button>
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      );
    };


    export const DialogManagerRenderer = () => {
      const dialogs = useDialogStore((state) => state.dialogs);
      const activeDialog = dialogs.length > 0 ? dialogs[0] : null; // Affiche seulement le premier dialogue

      if (!activeDialog) {
        return null;
      }

      return <DialogInstance key={activeDialog.id} dialog={activeDialog} />;
    };
    ```

4.  **Intégrer le `DialogManagerRenderer` dans votre Layout (`app/layout.tsx`) :**
    Pour qu'il soit disponible sur toutes les pages.

    ```typescript
    // app/layout.tsx (ou dans un composant Provider)
    import { DialogManagerRenderer } from '@/components/DialogRenderer';
    import { Toaster } from "@/components/ui/sonner"; // Si vous utilisez Sonner pour les notifications

    export default function RootLayout({ children }: { children: React.ReactNode }) {
      return (
 федеральный закон                                                                        
        <html lang="en">
          <body>
            {children}
            <DialogManagerRenderer />
            <Toaster /> {/* Pour les notifications Sonner */}
          </body>
        </html>
      );
    }
    ```

5.  **Utilisation (`components/MyComponent.tsx`) :**

    ```typescript
    "use client";
    import { Button } from "@/components/ui/button";
    import { dialogManager } from '@/store/dialogStore'; // Utilisez le helper
    import { toast } from "sonner"; // Pour les notifications

    export const MyComponent = () => {
      const handleDelete = () => {
        dialogManager.add({
          title: "Êtes-vous sûr ?",
          description: "Cette action est irréversible et supprimera définitivement l'organisation.",
          action: {
            label: "Supprimer",
            variant: "destructive",
            onClick: async () => {
              // Simuler une action asynchrone
              await new Promise(resolve => setTimeout(resolve, 1000));
              console.log("Organisation supprimée !");
              toast.success("Organisation supprimée avec succès !");
              // throw new Error("Test error"); // Pour tester la gestion d'erreur
            },
          },
          confirmText: "DELETE", // L'utilisateur devra taper "DELETE"
        });
      };

      const handleRename = () => {
        dialogManager.add({
          title: "Renommer l'élément",
          input: {
            label: "Nouveau nom :",
            defaultValue: "Ancien Nom",
            placeholder: "Entrez le nouveau nom"
          },
          action: {
            label: "Renommer",
            onClick: (newName) => {
              if (newName && newName.trim() !== "") {
                console.log("Nouvel nom :", newName);
                toast.info(`Élément renommé en : ${newName}`);
              } else {
                toast.error("Le nom ne peut pas être vide.");
                // Pour ne pas fermer la modale en cas d'erreur de validation, il faut la ré-ajouter ou ne pas la supprimer
                // Ici, on la laisse se fermer, mais c'est une amélioration possible.
              }
            },
          },
        });
      };

      const handleCustom = () => {
        dialogManager.add({
          title: "Dialogue Personnalisé",
          customContent: (
            <div className="my-4 p-4 bg-blue-100 border border-blue-300 rounded">
              <p className="text-blue-700">Ceci est un contenu totalement personnalisé injecté dans la modale !</p>
              <img src="https://via.placeholder.com/150" alt="Placeholder" className="mt-2" />
            </div>
          ),
          action: {
            label: "OK Super",
            onClick: () => {
              toast.info("Dialogue personnalisé fermé.");
            }
          }
        });
      }

      return (
        <div className="p-4 space-y-2">
          <Button variant="destructive" onClick={handleDelete}>
            Supprimer l'organisation (avec confirmText)
          </Button>
          <Button onClick={handleRename}>
            Renommer (avec input)
          </Button>
          <Button variant="secondary" onClick={handleCustom}>
            Dialogue avec contenu personnalisé
          </Button>
        </div>
      );
    };
    ```

**Comment utiliser ce système pour d'autres choses :**

Le principe fondamental est d'avoir un état global (géré par Zustand ici) qui pilote l'affichage d'un composant UI. Vous pouvez adapter ce pattern :

1.  **Différents types de Dialogues :**
    *   Vous pourriez avoir différents types de dialogues (info, succès, erreur) en ajoutant un champ `type` à `DialogConfig`.
    *   Le `DialogInstance` pourrait alors adapter son apparence (icônes, couleurs) en fonction de ce type.
    *   Ou créer des fonctions helper distinctes : `dialogManager.confirm()`, `dialogManager.prompt()`, `dialogManager.alert()`.

2.  **Notifications / Toasts :**
    *   C'est un cas d'usage très similaire. Une librairie comme `Sonner` (que j'ai incluse dans l'exemple `shadcn-ui add`) fonctionne sur ce principe : vous appelez `toast.success("Message")` de n'importe où, et un composant `<Toaster />` (placé dans le layout) gère l'affichage. Le "Dialog Manager" est essentiellement une version maison et plus complexe de ce pattern, spécifiquement pour les modales de type `AlertDialog`.

3.  **Panneaux latéraux (Side Panels / Drawers) :**
    *   Si vous voulez ouvrir un panneau latéral depuis n'importe où (par exemple, un panneau de profil utilisateur, un panier d'achat), vous pouvez utiliser un store Zustand pour stocker l'état d'ouverture et le contenu/props du panneau. Un composant `SidePanelRenderer` dans votre layout l'afficherait.

4.  **Lecteur de musique global :**
    *   Un store Zustand pourrait contenir la piste actuelle, l'état de lecture (play/pause), etc. Un composant `GlobalMusicPlayer` s'abonnerait à ce store pour afficher les contrôles et jouer la musique. Les actions de contrôle pourraient être appelées de n'importe où.

5.  **Modales de Formulaire complexes :**
    *   Au lieu de `customContent: ReactNode`, vous pourriez avoir une prop `formComponent: (onSubmit: (data: any) => void, onCancel: () => void) => ReactNode`.
    *   `dialogManager.add({ title: "Créer utilisateur", formComponent: (submit, cancel) => <UserForm onSubmit={submit} onCancel={cancel} />, ... })`.
    *   Le `DialogInstance` injecterait alors ce `formComponent` et gérerait la soumission.

**En résumé, la clé est :**

*   **Un store centralisé** (Zustand, Redux, Jotai, ou même Context API pour des cas plus simples) pour l'état de l'élément UI que vous voulez contrôler globalement.
*   **Un composant "Renderer"** placé à un niveau élevé de l'arbre (souvent le layout) qui lit ce store et affiche l'élément UI en fonction de l'état.
*   **Des fonctions d'action (le "Manager")** qui modifient le store pour déclencher l'affichage ou masquer l'élément UI.

Ce "Dialog Manager" est une implémentation élégante de ce pattern pour les boîtes de dialogue d'alerte, rendant le code plus propre, plus maintenable et l'expérience développeur plus agréable.

