"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { benefits } from "@/lib/constants";
import { SlideInWrapper, StaggeredWrapper, FadeInWrapper, ParallaxBackground } from "@/components/ui/parallax-wrapper";

export function BenefitsSection() {
  return (
    <section className="section-spacing bg-muted/20">
      <div className="section-container">
        {/* Section Header */}
        <SlideInWrapper className="section-header">
          <Badge variant="outline" className="mb-4">
            Benefits
          </Badge>
          <h2 className="heading-section mb-6">
            Why influencers choose
            <span className="text-primary"> our marketplace</span>
          </h2>
          <p className="text-lead min-w-3xl mx-auto">
            Join thousands of content creators who have accelerated their TikTok success
            with our premium account marketplace.
          </p>
        </SlideInWrapper>

        {/* Benefits Grid */}
        <StaggeredWrapper
          className="grid-3-cols section-content-wide"
          staggerDelay={0.2}
        >
          {benefits.map((benefit) => {
            const IconComponent = benefit.iconUrl;
            return (
              <Card
                key={benefit.id}
                className={`relative overflow-hidden border-0 transition-all duration-500 hover:scale-105 hover:shadow-2xl ${
                  benefit.light
                    ? 'bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800'
                    : 'bg-gradient-to-br from-background to-muted/50'
                }`}
              >
                <CardContent className="p-8 relative z-10">
                  {/* Icon */}
                  <div className="w-16 h-16 rounded-2xl bg-primary/10 flex items-center justify-center mb-6 hover:bg-primary/20 transition-colors duration-300">
                    <IconComponent className="w-8 h-8 text-primary" />
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-bold mb-4 hover:text-primary transition-colors duration-300">
                    {benefit.title}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {benefit.text}
                  </p>

                  {/* Decorative Elements with Parallax */}
                  <ParallaxBackground speed={0.3}>
                    <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary/5 to-transparent rounded-full transform translate-x-16 -translate-y-16 hover:scale-150 transition-transform duration-500" />
                  </ParallaxBackground>

                  <ParallaxBackground speed={0.4}>
                    <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-500/5 to-transparent rounded-full transform -translate-x-12 translate-y-12 hover:scale-150 transition-transform duration-500" />
                  </ParallaxBackground>
                </CardContent>

                {/* Hover Overlay */}
                <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-blue-500/5 opacity-0 hover:opacity-100 transition-opacity duration-300" />

                {/* Border Glow Effect */}
                <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/20 to-blue-500/20 opacity-0 hover:opacity-100 transition-opacity duration-300 blur-xl -z-10" />
              </Card>
            );
          })}
        </StaggeredWrapper>

        {/* Call to Action */}
        <FadeInWrapper delay={0.8} className="text-center mt-16">
          <div className="inline-flex items-center gap-4 p-6 bg-gradient-to-r from-primary/10 to-blue-500/10 rounded-2xl backdrop-blur-sm border border-primary/20">
            <div className="text-left">
              <h3 className="text-lg font-semibold mb-1">Ready to boost your influence?</h3>
              <p className="text-sm text-muted-foreground">Join thousands of creators using our marketplace</p>
            </div>
            <div className="flex gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse delay-100" />
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse delay-200" />
            </div>
          </div>
        </FadeInWrapper>
      </div>
    </section>
  );
}
