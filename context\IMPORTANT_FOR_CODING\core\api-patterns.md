# 🔌 API Route Patterns

This guide covers best practices for creating API routes in Hexa TikPay using Next.js 15 App Router.

## 📋 Overview

Our API routes follow these principles:
- **RESTful Design**: Standard HTTP methods and status codes
- **Type Safety**: Full TypeScript integration with Zod validation
- **Authentication**: Clerk-based authentication for protected routes
- **Error Handling**: Comprehensive error management and logging
- **Performance**: Optimized queries and response caching

## ❌ Bad Example

```typescript
// Poor API route - no validation, error handling, or types
export async function GET(request: Request) {
  const data = await db.select().from(accounts);
  return Response.json(data);
}

export async function POST(request: Request) {
  const body = await request.json();
  const result = await db.insert(accounts).values(body);
  return Response.json(result);
}
```

## ✅ Good Example

```typescript
// Well-structured API route with proper patterns
import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/DB';
import { logger } from '@/lib/Logger';
import { userAccountsSchema } from '@/models/Schema';
import { AccountQueryValidation, AccountValidation } from '@/validations/AccountValidation';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const queryData = Object.fromEntries(searchParams.entries());
    const parse = AccountQueryValidation.safeParse(queryData);

    if (!parse.success) {
      return NextResponse.json(
        { success: false, error: 'Invalid parameters', details: parse.error.format() },
        { status: 400 }
      );
    }

    const accounts = await getUserAccounts(userId, parse.data);

    return NextResponse.json({
      success: true,
      data: accounts,
    });
  } catch (error) {
    logger.error('Error fetching accounts:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

## 🏗️ API Route Structure

### 1. File Organization
```
app/[locale]/(marketing)/api/
├── accounts/
│   ├── route.ts              # CRUD operations for accounts
│   └── [accountId]/
│       ├── route.ts          # Operations for specific account
│       └── analytics/
│           └── route.ts      # Account analytics endpoints
├── subscriptions/
│   ├── route.ts              # Subscription management
│   └── [subscriptionId]/
│       └── route.ts          # Specific subscription operations
└── webhooks/
    ├── stripe/
    │   └── route.ts          # Stripe webhook handler
    └── clerk/
        └── route.ts          # Clerk webhook handler
```

### 2. HTTP Methods Implementation

#### GET - Retrieve Data
```typescript
export async function GET(request: NextRequest) {
  try {
    // 1. Authentication check
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 2. Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryData = Object.fromEntries(searchParams.entries());
    const parse = QueryValidation.safeParse(queryData);

    if (!parse.success) {
      return NextResponse.json(
        { success: false, error: 'Invalid parameters', details: parse.error.format() },
        { status: 400 }
      );
    }

    // 3. Fetch data with proper error handling
    const data = await fetchDataWithFilters(userId, parse.data);

    // 4. Return structured response
    return NextResponse.json({
      success: true,
      data,
      pagination: data.pagination,
    });
  } catch (error) {
    logger.error('GET /api/resource error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch data' },
      { status: 500 }
    );
  }
}
```

#### POST - Create Resource
```typescript
export async function POST(request: NextRequest) {
  try {
    // 1. Authentication check
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 2. Parse and validate request body
    const json = await request.json();
    const parse = CreateResourceValidation.safeParse(json);

    if (!parse.success) {
      return NextResponse.json(
        { success: false, error: 'Validation failed', details: parse.error.format() },
        { status: 422 }
      );
    }

    // 3. Business logic validation
    const existingResource = await checkResourceExists(userId, parse.data);
    if (existingResource) {
      return NextResponse.json(
        { success: false, error: 'Resource already exists' },
        { status: 409 }
      );
    }

    // 4. Create resource
    const newResource = await createResource(userId, parse.data);

    // 5. Log success and return response
    logger.info('Resource created successfully', {
      resourceId: newResource.id,
      userId,
    });

    return NextResponse.json({
      success: true,
      data: newResource,
      message: 'Resource created successfully',
    }, { status: 201 });
  } catch (error) {
    logger.error('POST /api/resource error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create resource' },
      { status: 500 }
    );
  }
}
```

## 🔒 Authentication Patterns

### Protected Routes
```typescript
// Standard authentication check
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Continue with authenticated logic
  } catch (error) {
    // Error handling
  }
}
```

### Admin-Only Routes
```typescript
// Admin authentication check
export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const isAdmin = sessionClaims?.metadata?.role === 'admin';
    if (!isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Continue with admin logic
  } catch (error) {
    // Error handling
  }
}
```

## 📊 Response Patterns

### Success Responses
```typescript
// Single resource
return NextResponse.json({
  success: true,
  data: resource,
  message: 'Operation completed successfully',
});

// Multiple resources with pagination
return NextResponse.json({
  success: true,
  data: resources,
  pagination: {
    page: 1,
    limit: 20,
    total: 100,
    totalPages: 5,
    hasNext: true,
    hasPrev: false,
  },
});
```

### Error Responses
```typescript
// Validation error
return NextResponse.json({
  success: false,
  error: 'Validation failed',
  details: validationErrors,
}, { status: 422 });

// Not found error
return NextResponse.json({
  success: false,
  error: 'Resource not found',
}, { status: 404 });

// Server error
return NextResponse.json({
  success: false,
  error: 'Internal server error',
}, { status: 500 });
```

## 🚀 Performance Optimization

### Caching Strategies
```typescript
// Cache static data
export async function GET() {
  try {
    const data = await getCachedData();

    return NextResponse.json(
      { success: true, data },
      {
        headers: {
          'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400',
        },
      }
    );
  } catch (error) {
    // Error handling
  }
}
```

### Database Query Optimization
```typescript
// Use specific column selection and proper indexing
export async function GET(request: NextRequest) {
  try {
    const accounts = await db
      .select({
        id: userAccountsSchema.id,
        accountHandle: userAccountsSchema.accountHandle,
        accountType: userAccountsSchema.accountType,
        monetizationStatus: userAccountsSchema.monetizationStatus,
      })
      .from(userAccountsSchema)
      .where(eq(userAccountsSchema.userId, userId))
      .limit(20);

    return NextResponse.json({ success: true, data: accounts });
  } catch (error) {
    // Error handling
  }
}
```

## ✅ API Route Checklist

- [ ] Authentication is properly implemented
- [ ] Input validation with Zod schemas
- [ ] Proper HTTP status codes are used
- [ ] Error handling covers all scenarios
- [ ] Logging is implemented for debugging
- [ ] TypeScript types are used throughout
- [ ] Database operations are optimized
- [ ] Response format is consistent
- [ ] Security considerations are addressed
- [ ] Performance optimizations are applied

## 📚 See Also

- [Security Guidelines](../core/security-guidelines.md) - Security implementation
- [Database Patterns](./database-patterns.md) - Database operations
- [Validation Patterns](./validation-patterns.md) - Input validation strategies
- [Testing Patterns](./testing-patterns.md) - API testing strategies
