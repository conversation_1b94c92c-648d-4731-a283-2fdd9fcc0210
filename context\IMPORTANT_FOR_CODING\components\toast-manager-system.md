# 🍞 Toast Manager System

Ce document explique l'implémentation du système Toast Manager pour Hexa TikPay, qui permet d'afficher des notifications toast depuis les Server Components et Server Actions vers les Client Components via des cookies.

## 🎯 Problème Résolu

### **Défi : Communication Serveur → Client**
Les Server Components et Server Actions ne peuvent pas directement manipuler le DOM ou déclencher des notifications côté client. Il faut un mécanisme pour communiquer le besoin d'afficher un toast du serveur vers le client.

### **Solution : Cookies comme Pont**
```tsx
// SERVEUR (Server Action)
await serverToast('Account created successfully!', 'success');

// CLIENT (automatiquement affiché)
// Toast apparaît avec le message et le style approprié
```

## 🏗️ Architecture du Système

### **1. Fonction Server Toast (lib/server-toast.ts)**

```typescript
import { revalidatePath } from 'next/cache';
import { cookies } from 'next/headers';

export type ToastType = 'success' | 'error' | 'warning' | 'info' | 'loading';

export type ToastData = {
  id: string;
  message: string;
  type: ToastType;
  title?: string;
  description?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: string; // Sera sérialisé comme string pour les cookies
  };
  timestamp: number;
};

/**
 * Fonction principale pour créer un toast depuis le serveur
 * @param message Message principal du toast
 * @param type Type de toast (success, error, warning, info, loading)
 * @param options Options supplémentaires
 */
export async function serverToast(
  message: string,
  type: ToastType = 'info',
  options?: {
    title?: string;
    description?: string;
    duration?: number;
    action?: {
      label: string;
      onClick: string;
    };
  }
): Promise<void> {
  const cookieStore = await cookies();

  // Générer un ID unique pour ce toast
  const toastId = crypto.randomUUID();
  const cookieName = `toast-${toastId}`;

  // Créer les données du toast
  const toastData: ToastData = {
    id: toastId,
    message,
    type,
    title: options?.title,
    description: options?.description,
    duration: options?.duration,
    action: options?.action,
    timestamp: Date.now()
  };

  // Stocker dans un cookie
  cookieStore.set(cookieName, JSON.stringify(toastData), {
    path: '/',
    maxAge: 60 * 60 * 24, // 24 heures
    httpOnly: false, // Accessible côté client
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax'
  });
}

/**
 * Fonctions de convenance pour différents types de toast
 */
export const toastSuccess = (message: string, options?: Omit<Parameters<typeof serverToast>[2], never>) =>
  serverToast(message, 'success', options);

export const toastError = (message: string, options?: Omit<Parameters<typeof serverToast>[2], never>) =>
  serverToast(message, 'error', options);

export const toastWarning = (message: string, options?: Omit<Parameters<typeof serverToast>[2], never>) =>
  serverToast(message, 'warning', options);

export const toastInfo = (message: string, options?: Omit<Parameters<typeof serverToast>[2], never>) =>
  serverToast(message, 'info', options);

export const toastLoading = (message: string, options?: Omit<Parameters<typeof serverToast>[2], never>) =>
  serverToast(message, 'loading', options);

/**
 * Fonction pour supprimer un toast spécifique (Server Action)
 */
export async function dismissToast(toastId: string): Promise<void> {
  'use server';

  const cookieStore = await cookies();
  const cookieName = `toast-${toastId}`;

  try {
    cookieStore.delete(cookieName);
  } catch (error) {
    console.error('Failed to dismiss toast:', error);
  }
}

/**
 * Fonction pour nettoyer tous les toasts expirés
 */
export async function cleanupExpiredToasts(): Promise<void> {
  'use server';

  const cookieStore = await cookies();
  const allCookies = cookieStore.getAll();
  const now = Date.now();
  const maxAge = 60 * 60 * 24 * 1000; // 24 heures en millisecondes

  for (const cookie of allCookies) {
    if (cookie.name.startsWith('toast-')) {
      try {
        const toastData: ToastData = JSON.parse(cookie.value);
        if (now - toastData.timestamp > maxAge) {
          cookieStore.delete(cookie.name);
        }
      } catch (error) {
        // Cookie corrompu, le supprimer
        cookieStore.delete(cookie.name);
      }
    }
  }
}
```

### **2. Server Component Toast Reader (components/toast-manager/server-toaster.tsx)**

```tsx
import type { ToastData } from '@/lib/server-toast';
import { cookies } from 'next/headers';
import { dismissToast } from '@/lib/server-toast';
import { ClientToasts } from './client-toasts';

/**
 * Server Component qui lit les cookies de toast et les passe au client
 * Doit être placé dans le layout principal
 */
export async function ServerToaster() {
  const cookieStore = await cookies();
  const allCookies = cookieStore.getAll();

  // Filtrer et parser les cookies de toast
  const toasts: (ToastData & { dismiss: () => Promise<void> })[] = [];

  for (const cookie of allCookies) {
    if (cookie.name.startsWith('toast-')) {
      try {
        const toastData: ToastData = JSON.parse(cookie.value);

        // Créer une fonction de dismiss pour ce toast spécifique
        const dismiss = async () => {
          'use server';
          await dismissToast(toastData.id);
        };

        toasts.push({
          ...toastData,
          dismiss
        });
      } catch (error) {
        console.error('Failed to parse toast cookie:', cookie.name, error);
        // Supprimer le cookie corrompu
        cookieStore.delete(cookie.name);
      }
    }
  }

  // Trier par timestamp (plus récent en premier)
  toasts.sort((a, b) => b.timestamp - a.timestamp);

  return <ClientToasts toasts={toasts} />;
}
```

### **3. Client Component Toast Display (components/toast-manager/client-toasts.tsx)**

```tsx
'use client';

import type { ToastData } from '@/lib/server-toast';
import { useTranslations } from 'next-intl';
import { useEffect, useRef } from 'react';
import { toast } from 'sonner';

type ClientToastsProps = {
  toasts: (ToastData & { dismiss: () => Promise<void> })[];
};

/**
 * Client Component qui affiche les toasts en utilisant Sonner
 */
export function ClientToasts({ toasts }: ClientToastsProps) {
  const t = useTranslations('toasts');
  const displayedToasts = useRef(new Set<string>());

  useEffect(() => {
    for (const toastData of toasts) {
      // Éviter d'afficher le même toast plusieurs fois
      if (displayedToasts.current.has(toastData.id)) {
        continue;
      }

      displayedToasts.current.add(toastData.id);

      // Fonction de dismiss qui appelle le Server Action
      const handleDismiss = async () => {
        try {
          await toastData.dismiss();
        } catch (error) {
          console.error('Failed to dismiss toast:', error);
        }
      };

      // Afficher le toast selon son type
      switch (toastData.type) {
        case 'success':
          toast.success(toastData.message, {
            id: toastData.id,
            description: toastData.description,
            duration: toastData.duration,
            onDismiss: handleDismiss,
            action: toastData.action ? {
              label: toastData.action.label,
              onClick: () => {
                // Exécuter l'action (peut être une navigation, etc.)
                if (toastData.action?.onClick) {
                  eval(toastData.action.onClick); // Attention: eval est dangereux, à améliorer
                }
              }
            } : undefined
          });
          break;

        case 'error':
          toast.error(toastData.message, {
            id: toastData.id,
            description: toastData.description,
            duration: toastData.duration || 5000, // Plus long pour les erreurs
            onDismiss: handleDismiss
          });
          break;

        case 'warning':
          toast.warning(toastData.message, {
            id: toastData.id,
            description: toastData.description,
            duration: toastData.duration,
            onDismiss: handleDismiss
          });
          break;

        case 'loading':
          toast.loading(toastData.message, {
            id: toastData.id,
            description: toastData.description,
            duration: Infinity, // Les loading toasts ne disparaissent pas automatiquement
            onDismiss: handleDismiss
          });
          break;

        default:
          toast(toastData.message, {
            id: toastData.id,
            description: toastData.description,
            duration: toastData.duration,
            onDismiss: handleDismiss
          });
      }
    }
  }, [toasts, t]);

  // Ce composant ne rend rien visuellement
  return null;
}
```

## 🚀 Intégration dans l'Application

### **1. Installation des Dépendances**

```bash
# Installer Sonner pour les toasts
pnpm add sonner

# Types pour TypeScript
pnpm add -D @types/node
```

### **2. Configuration dans le Layout Principal**

```tsx
import { Toaster } from 'sonner';
// app/[locale]/layout.tsx
import { ServerToaster } from '@/components/toast-manager/server-toaster';

export default function RootLayout({
  children,
  params: { locale }
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider>
          <PostHogProvider>
            {children}
          </PostHogProvider>

          {/* Toast System */}
          <Toaster
            position="top-right"
            richColors
            closeButton
            expand={false}
            visibleToasts={5}
          />
          <ServerToaster />

          {/* Dialog System */}
          <DialogManagerRenderer />
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
```

## 📝 Exemples d'Utilisation

### **1. Dans une Server Action**

```tsx
// app/actions/account-actions.ts
'use server';

import { revalidatePath } from 'next/cache';
import { serverToast, toastError, toastSuccess } from '@/lib/server-toast';

export async function createAccount(formData: FormData) {
  try {
    const handle = formData.get('handle') as string;

    // Logique de création de compte
    await createAccountInDatabase(handle);

    // Toast de succès
    await toastSuccess('Account created successfully!', {
      description: `@${handle} has been added to your portfolio`,
      duration: 4000
    });

    revalidatePath('/dashboard/accounts');
  } catch (error) {
    // Toast d'erreur
    await toastError('Failed to create account', {
      description: error.message,
      duration: 6000
    });
  }
}

export async function deleteAccount(accountId: string) {
  try {
    await deleteAccountFromDatabase(accountId);

    await toastSuccess('Account deleted', {
      description: 'The account has been permanently removed'
    });

    revalidatePath('/dashboard/accounts');
  } catch (error) {
    await toastError('Failed to delete account', {
      description: 'Please try again later'
    });
  }
}
```

### **2. Dans un Server Component avec Form**

```tsx
// components/account-form.tsx
import { createAccount } from '@/app/actions/account-actions';

export function AccountForm() {
  return (
    <form action={createAccount} className="space-y-4">
      <div>
        <label htmlFor="handle">Account Handle</label>
        <input
          id="handle"
          name="handle"
          type="text"
          placeholder="username"
          required
        />
      </div>

      <button type="submit">
        Create Account
      </button>
    </form>
  );
}
```

### **3. Toast avec Action**

```tsx
// Server Action avec toast interactif
export async function claimTokens(amount: string) {
  try {
    await processTokenClaim(amount);

    await serverToast(`${amount} UMA tokens claimed!`, 'success', {
      description: 'Tokens have been transferred to your wallet',
      action: {
        label: 'View Transaction',
        onClick: 'window.open("/transactions", "_blank")'
      }
    });
  } catch (error) {
    await toastError('Claim failed', {
      description: 'Please check your wallet connection'
    });
  }
}
```

## 🚀 Installation et Configuration

### **1. Installation des Dépendances**

```bash
# Installer Sonner pour les toasts
pnpm add sonner

# Types pour TypeScript (si nécessaire)
pnpm add -D @types/node
```

### **2. Configuration dans le Layout Principal**

```tsx
import { Toaster } from 'sonner';
// app/[locale]/layout.tsx
import { ServerToaster } from '@/components/toast-manager/server-toaster';

export default function RootLayout({
  children,
  params: { locale }
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider>
          <PostHogProvider>
            {children}
          </PostHogProvider>

          {/* Toast System */}
          <Toaster
            position="top-right"
            richColors
            closeButton
            expand={false}
            visibleToasts={5}
            duration={4000}
          />
          <ServerToaster />

          {/* Dialog System */}
          <DialogManagerRenderer />
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
```

### **3. Utilisation avec le Hook Personnalisé**

```tsx
// Dans un Server Component ou Server Action
import { useServerToast } from '@/hooks/use-server-toast';

export async function createAccount(formData: FormData) {
  const toast = useServerToast();

  try {
    const handle = formData.get('handle') as string;
    await createAccountInDatabase(handle);

    // Toast de succès avec traductions
    await toast.accountCreated(handle);

    revalidatePath('/dashboard/accounts');
  } catch (error) {
    // Toast d'erreur avec traductions
    await toast.operationFailed('account creation', error.message);
  }
}
```

## ✅ Avantages du Système

1. **Communication Serveur → Client**: Pont via cookies pour les Server Components
2. **Persistance**: Les toasts survivent aux navigations et rechargements
3. **Type Safety**: TypeScript complet avec validation
4. **Sécurité**: Actions sécurisées, pas d'eval() dangereux
5. **Internationalisation**: Support i18n intégré
6. **Performance**: Nettoyage automatique des cookies expirés
7. **Flexibilité**: Support des actions interactives
8. **Accessibilité**: Composants Sonner accessibles

## 🧪 Tests et Qualité

### **Tests Unitaires**
```bash
# Exécuter les tests du Toast Manager
npm test toast-manager

# Tests avec couverture
npm test toast-manager -- --coverage
```

### **Tests d'Intégration**
```tsx
// Exemple de test d'intégration
import { render, screen } from '@testing-library/react';
import { ServerToaster } from '@/components/toast-manager/server-toaster';
import { serverToast } from '@/lib/server-toast';

test('should display server toast', async () => {
  // Simuler un cookie de toast
  const mockCookies = {
    getAll: () => [{
      name: 'toast-123',
      value: JSON.stringify({
        id: '123',
        message: 'Test toast',
        type: 'success',
        timestamp: Date.now()
      })
    }]
  };

  // Test du rendu
  render(<ServerToaster />);

  // Vérifier que le toast est traité
  expect(mockCookies.getAll).toHaveBeenCalled();
});
```

## 📋 Checklist d'Implémentation

### **Installation**
- [ ] Installer Sonner: `pnpm add sonner`
- [ ] Créer les fichiers du système Toast Manager
- [ ] Ajouter le ServerToaster dans le layout principal
- [ ] Configurer les traductions (en/fr)

### **Configuration**
- [ ] Intégrer Toaster de Sonner dans `app/[locale]/layout.tsx`
- [ ] Ajouter ServerToaster pour lire les cookies
- [ ] Configurer les traductions dans `locales/en.json` et `locales/fr.json`
- [ ] Créer le hook personnalisé `useServerToast`

### **Utilisation**
- [ ] Remplacer les notifications existantes par le système Toast
- [ ] Utiliser le hook personnalisé pour les cas d'usage courants
- [ ] Tester tous les types de toasts (success, error, warning, info, loading)
- [ ] Vérifier la persistance lors des navigations

### **Tests**
- [ ] Tests unitaires pour les fonctions serveur
- [ ] Tests de composants pour ClientToasts
- [ ] Tests d'intégration pour le workflow complet
- [ ] Tests de sécurité pour les actions
- [ ] Tests de nettoyage des cookies expirés

## 🔧 Personnalisation Avancée

### **Configuration Sonner**
```tsx
// Configuration avancée du Toaster
<Toaster
  position="top-right"
  richColors={true}
  closeButton={true}
  expand={false}
  visibleToasts={5}
  duration={4000}
  theme="system" // 'light', 'dark', 'system'
  className="custom-toaster"
  toastOptions={{
    className: 'custom-toast',
    style: {
      background: 'hsl(var(--background))',
      border: '1px solid hsl(var(--border))',
      color: 'hsl(var(--foreground))'
    }
  }}
/>;
```

### **Actions Personnalisées Sécurisées**
```tsx
// Ajouter de nouvelles actions sécurisées
const allowedActions = {
  'window.open': (url: string, target?: string) => window.open(url, target),
  'window.location.href': (url: string) => {
    window.location.href = url;
  },
  'window.location.reload': () => window.location.reload(),
  'history.back': () => window.history.back(),
  'copyToClipboard': (text: string) => navigator.clipboard.writeText(text),
  'downloadFile': (url: string) => {
    const a = document.createElement('a');
    a.href = url;
    a.download = '';
    a.click();
  }
};
```

### **Gestion d'Erreurs Avancée**
```tsx
// Gestion d'erreurs avec retry et escalation
export async function handleAsyncOperation() {
  let retryCount = 0;
  const maxRetries = 3;

  while (retryCount < maxRetries) {
    try {
      await riskyOperation();
      await toastSuccess('Operation completed successfully');
      return;
    } catch (error) {
      retryCount++;

      if (retryCount < maxRetries) {
        await toastWarning(`Attempt ${retryCount} failed, retrying...`, {
          description: `${maxRetries - retryCount} attempts remaining`
        });
        await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
      } else {
        await toastError('Operation failed after multiple attempts', {
          description: 'Please contact support if the problem persists',
          action: {
            label: 'Contact Support',
            onClick: 'window.open("/support", "_blank")'
          }
        });
      }
    }
  }
}
```

## 🚨 Common Issues and Troubleshooting

### Issue: Duplicate Export Error

**Problem**: `toastLoading` function declared multiple times causing compilation errors.

**Error Example**:
```
error TS2300: Duplicate identifier 'toastLoading'
./lib/server-toast.ts:245:14 - error: toastLoading is defined multiple times
```

**Root Cause**: Multiple `toastLoading` function declarations in `lib/server-toast.ts`:
- Line 93-96: Generic convenience function
- Line 245-248: Specialized function for operations with infinite duration

**Solution**:
1. **Identify duplicates**: Search for multiple export declarations of the same function name
2. **Consolidate functionality**: Merge the functionality into a single export
3. **Use descriptive names**: Rename specialized functions (e.g., `toastLoadingOperation`)
4. **Update imports**: Ensure all imports reference the correct function name

**Fixed Implementation**:
```typescript
// Single toastLoading function with infinite duration by default
export const toastLoading = (
  message: string,
  options?: Omit<Parameters<typeof serverToast>[2], never>,
) => serverToast(message, 'loading', { duration: Infinity, ...options });

// Specialized function with descriptive name
export const toastLoadingOperation = (operation: string) =>
  serverToast(`${operation}...`, 'loading', {
    duration: Infinity,
  });
```

### Issue: Missing Dependencies

**Problem**: Cannot resolve module errors for UI components and libraries.

**Common Missing Dependencies**:
```bash
# Radix UI Components (required for shadcn/ui)
pnpm add @radix-ui/react-slot @radix-ui/react-aspect-ratio @radix-ui/react-avatar
pnpm add @radix-ui/react-checkbox @radix-ui/react-collapsible @radix-ui/react-dialog
pnpm add @radix-ui/react-dropdown-menu @radix-ui/react-label @radix-ui/react-popover
pnpm add @radix-ui/react-progress @radix-ui/react-radio-group @radix-ui/react-select
pnpm add @radix-ui/react-separator @radix-ui/react-slider @radix-ui/react-switch
pnpm add @radix-ui/react-tabs @radix-ui/react-toast @radix-ui/react-toggle
pnpm add @radix-ui/react-toggle-group @radix-ui/react-tooltip @radix-ui/react-accordion
pnpm add @radix-ui/react-alert-dialog @radix-ui/react-context-menu @radix-ui/react-hover-card
pnpm add @radix-ui/react-menubar @radix-ui/react-navigation-menu @radix-ui/react-scroll-area

# Other UI Libraries
pnpm add cmdk vaul react-resizable-panels next-themes input-otp
pnpm add framer-motion react-day-picker embla-carousel-react

# Testing and Development
pnpm add @testing-library/user-event nanoid
```

**Prevention**: Always check `components.json` and existing component imports before adding new UI components.

### Issue: Next.js 15 Async Cookies API Error

**Problem**: `Route "/[locale]/sign-up/[[...sign-up]]" used cookies().getAll(). cookies() should be awaited before using its value.`

**Root Cause**: Next.js 15 introduced a breaking change where the `cookies()` function became async and must be awaited before accessing its methods.

**Error Examples**:
```
Error: Route used `cookies().getAll()`. `cookies()` should be awaited before using its value.
Property 'set' does not exist on type 'Promise<ReadonlyRequestCookies>'
Property 'getAll' does not exist on type 'Promise<ReadonlyRequestCookies>'
```

**Solution**: Add `await` when accessing cookies in all Server Components and Server Actions:

```typescript
// ❌ Incorrect (Next.js 14 and earlier)
const cookieStore = cookies();
const allCookies = cookieStore.getAll();
cookieStore.set(name, value);
cookieStore.delete(name);

// ✅ Correct (Next.js 15+)
const cookieStore = await cookies();
const allCookies = cookieStore.getAll();
cookieStore.set(name, value);
cookieStore.delete(name);
```

**Files Updated for Next.js 15 Compatibility**:
- `components/toast-manager/server-toaster.tsx`
- `lib/server-toast.ts` (all functions: `serverToast`, `dismissToast`, `cleanupExpiredToasts`, `getActiveToasts`, `clearAllToasts`)

### Issue: Cookie Store Errors (Legacy)

**Problem**: `Property 'set' does not exist on type 'Promise<ReadonlyRequestCookies>'`

**Solution**: Add `await` when accessing cookies in Server Actions:
```typescript
// ❌ Incorrect
const cookieStore = cookies();
cookieStore.set(name, value);

// ✅ Correct
const cookieStore = await cookies();
cookieStore.set(name, value);
```

### Issue: Server Actions Not Working

**Problem**: Toast notifications not appearing after Server Action execution.

**Solution**:
1. Ensure Server Actions are properly marked with `'use server'`
2. Use `await` when calling toast functions
3. Check that cookies are being set correctly
4. Verify `ServerToaster` is included in layout

## 📚 Ressources Supplémentaires

- **[Sonner Documentation](https://sonner.emilkowal.ski/)** - Bibliothèque de toasts
- **[Next.js Cookies](https://nextjs.org/docs/app/api-reference/functions/cookies)** - Gestion des cookies
- **[Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations)** - Actions serveur Next.js
- **[next-intl](https://next-intl-docs.vercel.app/)** - Internationalisation

---

**Le système Toast Manager révolutionne les notifications dans Hexa TikPay, permettant une communication fluide entre le serveur et le client avec persistance, sécurité et internationalisation complètes.**
