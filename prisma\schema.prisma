// TikTok Account Marketplace - Prisma Schema
// B2B2C Platform for Pre-monetized TikTok Accounts

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// ============================================================================
// USER MANAGEMENT & AUTHENTICATION
// ============================================================================

enum UserRole {
  ADMIN
  STANDARD
  PREMIUM
  ENTERPRISE
}

enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  CANCELLED
  EXPIRED
  TRIAL
}

model User {
  id                String              @id @default(cuid())
  email             String              @unique
  emailVerified     DateTime?
  name              String?
  avatar            String?
  role              UserRole            @default(STANDARD)
  subscriptionTier  SubscriptionStatus  @default(INACTIVE)
  loyaltyPoints     Int                 @default(0)
  twoFactorEnabled  Boolean             @default(false)
  twoFactorSecret   String?

  // Profile Information
  company           String?
  industry          String?
  targetAudience    String?
  monthlyBudget     Float?
  experienceLevel   String?             // Beginner, Intermediate, Expert

  // Preferences
  preferredNiches   String[]            // Array of preferred TikTok niches
  notificationPrefs Json?               // Email, SMS, Push preferences

  // Timestamps
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  lastLoginAt       DateTime?

  // Relations
  orders            Order[]
  subscriptions     Subscription[]
  sessions          Session[]
  accounts          Account[]
  notifications     Notification[]
  newsArticles      NewsArticle[]

  @@map("users")
}

// ============================================================================
// TIKTOK ACCOUNT INVENTORY & PRODUCTS
// ============================================================================

enum AccountStatus {
  AVAILABLE
  RESERVED
  SOLD
  UNDER_REVIEW
  SUSPENDED
}

enum AccountNiche {
  LIFESTYLE
  FASHION
  FITNESS
  FOOD
  TRAVEL
  TECH
  GAMING
  BEAUTY
  COMEDY
  EDUCATION
  BUSINESS
  MUSIC
  DANCE
  PETS
  DIY
  SPORTS
  NEWS
  GENERAL
}

enum AccountTier {
  STARTER      // 1K-10K followers
  GROWTH       // 10K-50K followers
  INFLUENCER   // 50K-100K followers
  PREMIUM      // 100K-500K followers
  ELITE        // 500K+ followers
}

model TikTokAccount {
  id                String        @id @default(cuid())
  username          String        @unique
  displayName       String
  bio               String?
  profileImageUrl   String?

  // Account Metrics
  followerCount     Int
  followingCount    Int
  likesCount        Int
  videosCount       Int
  engagementRate    Float         // Percentage
  avgViews          Int
  avgLikes          Int
  avgComments       Int
  avgShares         Int

  // Classification
  niche             AccountNiche
  tier              AccountTier
  tags              String[]      // Additional tags for filtering

  // Monetization Data
  hasCreatorFund    Boolean       @default(false)
  hasLiveGifts      Boolean       @default(false)
  brandPartnerships Int           @default(0)
  estimatedValue    Float

  // Account Status
  status            AccountStatus @default(AVAILABLE)
  isVerified        Boolean       @default(false)
  accountAge        Int           // Days since creation
  lastActiveDate    DateTime?

  // Pricing
  basePrice         Float
  discountPrice     Float?
  isOnSale          Boolean       @default(false)

  // Security & Transfer
  credentials       Json?         // Encrypted account credentials
  transferNotes     String?

  // Timestamps
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  soldAt            DateTime?

  // Relations
  orders            OrderItem[]
  packages          AccountPackage[]

  @@map("tiktok_accounts")
}

model AccountPackage {
  id              String          @id @default(cuid())
  name            String
  description     String
  shortDescription String?

  // Package Configuration
  accountCount    Int             // Number of accounts in package
  totalFollowers  Int             // Combined followers
  avgEngagement   Float           // Average engagement rate
  niches          AccountNiche[]  // Included niches

  // Pricing
  originalPrice   Float
  discountPrice   Float?
  isPopular       Boolean         @default(false)

  // Package Benefits
  benefits        String[]        // List of benefits
  includes        String[]        // What's included

  // Availability
  isActive        Boolean         @default(true)
  stockCount      Int             @default(0)

  // Timestamps
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  // Relations
  accounts        TikTokAccount[]
  orders          OrderItem[]

  @@map("account_packages")
}

// ============================================================================
// ORDER MANAGEMENT & QUEUE SYSTEM
// ============================================================================

enum OrderStatus {
  PENDING
  PROCESSING
  VALIDATED
  ASSIGNED
  CUSTOMIZING
  READY
  DELIVERED
  COMPLETED
  CANCELLED
  REFUNDED
}

enum OrderPriority {
  STANDARD
  PREMIUM
  ENTERPRISE
  URGENT
}

model Order {
  id              String        @id @default(cuid())
  orderNumber     String        @unique

  // Customer Information
  userId          String
  user            User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Order Details
  status          OrderStatus   @default(PENDING)
  priority        OrderPriority @default(STANDARD)
  totalAmount     Float
  discountAmount  Float?        @default(0)
  finalAmount     Float

  // Payment Information
  paymentStatus   String?       // PENDING, PAID, FAILED, REFUNDED
  paymentMethod   String?       // STRIPE, PAYPAL, BANK_TRANSFER
  paymentId       String?       // External payment ID

  // Queue Management
  queuePosition   Int?
  estimatedDelivery DateTime?

  // Order Metadata
  customerNotes   String?
  adminNotes      String?

  // Timestamps
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  completedAt     DateTime?

  // Relations
  items           OrderItem[]
  notifications   Notification[]

  @@map("orders")
}

model OrderItem {
  id              String          @id @default(cuid())

  // Order Reference
  orderId         String
  order           Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)

  // Product Information
  accountId       String?
  account         TikTokAccount?  @relation(fields: [accountId], references: [id])

  packageId       String?
  package         AccountPackage? @relation(fields: [packageId], references: [id])

  // Item Details
  quantity        Int             @default(1)
  unitPrice       Float
  totalPrice      Float

  // Customization
  customizations  Json?           // Custom requirements

  // Delivery
  deliveredAt     DateTime?
  credentials     Json?           // Encrypted delivery data

  @@map("order_items")
}

// ============================================================================
// SUBSCRIPTION & PAYMENT SYSTEM
// ============================================================================

enum SubscriptionTier {
  BASIC
  PROFESSIONAL
  ENTERPRISE
}

model Subscription {
  id              String            @id @default(cuid())

  // User Reference
  userId          String
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Subscription Details
  tier            SubscriptionTier
  status          SubscriptionStatus

  // Pricing
  monthlyPrice    Float
  yearlyPrice     Float?
  currentPrice    Float

  // Billing
  billingCycle    String            // MONTHLY, YEARLY
  nextBillingDate DateTime?

  // External IDs
  stripeCustomerId      String?
  stripeSubscriptionId  String?

  // Timestamps
  startDate       DateTime          @default(now())
  endDate         DateTime?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  @@map("subscriptions")
}

// ============================================================================
// NOTIFICATION SYSTEM
// ============================================================================

enum NotificationType {
  ORDER_UPDATE
  PAYMENT_CONFIRMATION
  ACCOUNT_DELIVERY
  SUBSCRIPTION_RENEWAL
  PROMOTIONAL
  SYSTEM_ALERT
}

enum NotificationChannel {
  EMAIL
  SMS
  PUSH
  IN_APP
}

model Notification {
  id              String              @id @default(cuid())

  // Recipient
  userId          String
  user            User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Notification Details
  type            NotificationType
  channel         NotificationChannel
  title           String
  message         String

  // Metadata
  data            Json?               // Additional data

  // Status
  sent            Boolean             @default(false)
  sentAt          DateTime?
  read            Boolean             @default(false)
  readAt          DateTime?

  // Relations
  orderId         String?
  order           Order?              @relation(fields: [orderId], references: [id])

  // Timestamps
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt

  @@map("notifications")
}

// ============================================================================
// CMS & CONTENT MANAGEMENT
// ============================================================================

enum ContentStatus {
  DRAFT
  SCHEDULED
  PUBLISHED
  ARCHIVED
}

model NewsArticle {
  id              String        @id @default(cuid())
  title           String
  slug            String        @unique
  excerpt         String?
  content         String
  featuredImage   String?

  // Publishing
  status          ContentStatus @default(DRAFT)
  publishedAt     DateTime?
  scheduledFor    DateTime?

  // SEO
  metaTitle       String?
  metaDescription String?

  // Author
  authorId        String
  author          User          @relation(fields: [authorId], references: [id])

  // Timestamps
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  @@map("news_articles")
}

model Testimonial {
  id              String        @id @default(cuid())
  clientName      String
  clientTitle     String?
  clientCompany   String?
  clientAvatar    String?

  // Content
  content         String
  rating          Int           @default(5)

  // Display
  isActive        Boolean       @default(true)
  isFeatured      Boolean       @default(false)
  displayOrder    Int?

  // Timestamps
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  @@map("testimonials")
}

model Promotion {
  id              String        @id @default(cuid())
  title           String
  description     String
  bannerImage     String?

  // Promotion Details
  discountPercent Float?
  discountAmount  Float?
  promoCode       String?       @unique

  // Timing
  startDate       DateTime
  endDate         DateTime
  isActive        Boolean       @default(true)

  // Targeting
  targetTiers     UserRole[]
  maxUses         Int?
  currentUses     Int           @default(0)

  // Timestamps
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  @@map("promotions")
}

// ============================================================================
// AUTHENTICATION & SESSION MANAGEMENT
// ============================================================================

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// ============================================================================
// AUDIT LOGS & SECURITY
// ============================================================================

model AuditLog {
  id         String   @id @default(cuid())
  userId     String?
  action     String
  resource   String
  resourceId String?
  metadata   Json?
  ipAddress  String?
  userAgent  String?
  createdAt  DateTime @default(now())

  @@index([userId])
  @@index([action])
  @@index([createdAt])
  @@map("audit_logs")
}
