# 🔔 Dialog Manager System

Ce document explique l'implémentation du système DialogManager pour Hexa TikPay, qui simplifie la création et la gestion des boîtes de dialogue d'alerte et de confirmation.

## 🎯 Problème Résolu

### **Avant : Code Verbeux**
```tsx
// Code répétitif et verbeux pour chaque dialog
<AlertDialog>
  <AlertDialogTrigger asChild>
    <Button variant="destructive">Delete Account</Button>
  </AlertDialogTrigger>
  <AlertDialogContent>
    <AlertDialogHeader>
      <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
      <AlertDialogDescription>
        This action cannot be undone. This will permanently delete your account.
      </AlertDialogDescription>
    </AlertDialogHeader>
    <AlertDialogFooter>
      <AlertDialogCancel>Cancel</AlertDialogCancel>
      <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
    </AlertDialogFooter>
  </AlertDialogContent>
</AlertDialog>;
```

### **Après : Code Simplifié**
```tsx
// Code simple et réutilisable
DialogManager.add({
  title: 'Are you absolutely sure?',
  description: 'This action cannot be undone. This will permanently delete your account.',
  action: {
    label: 'Delete',
    onClick: handleDelete,
    variant: 'destructive'
  }
});
```

## 🏗️ Architecture du Système

### **1. Store Zustand (dialog-manager-store.ts)**

```typescript
import { nanoid } from 'nanoid';
import { create } from 'zustand';

export type DialogAction = {
  label: string;
  onClick: (inputValue?: string) => void | Promise<void>;
  variant?: 'default' | 'destructive' | 'secondary';
  disabled?: boolean;
};

export type DialogInput = {
  label: string;
  placeholder?: string;
  type?: 'text' | 'email' | 'password';
  required?: boolean;
};

export type DialogConfig = {
  id: string;
  title: string;
  description?: string;
  action: DialogAction;
  cancel?: {
    label: string;
    onClick?: () => void;
  };
  confirmText?: string; // Texte à taper pour confirmer
  input?: DialogInput; // Input générique
  icon?: React.ComponentType<{ className?: string }>;
  style?: 'default' | 'destructive' | 'warning' | 'info';
  loading?: boolean;
};

type DialogManagerState = {
  dialogs: DialogConfig[];
  addDialog: (config: Omit<DialogConfig, 'id' | 'loading'>) => void;
  removeDialog: (id: string) => void;
  setLoading: (id: string, loading: boolean) => void;
  executeAction: (id: string, inputValue?: string) => Promise<void>;
};

export const useDialogManager = create<DialogManagerState>((set, get) => ({
  dialogs: [],

  addDialog: (config) => {
    const dialog: DialogConfig = {
      ...config,
      id: nanoid(),
      loading: false,
      cancel: config.cancel || {
        label: 'Cancel',
        onClick: () => get().removeDialog(config.id)
      }
    };

    set(state => ({
      dialogs: [...state.dialogs, dialog]
    }));
  },

  removeDialog: (id) => {
    set(state => ({
      dialogs: state.dialogs.filter(dialog => dialog.id !== id)
    }));
  },

  setLoading: (id, loading) => {
    set(state => ({
      dialogs: state.dialogs.map(dialog =>
        dialog.id === id ? { ...dialog, loading } : dialog
      )
    }));
  },

  executeAction: async (id, inputValue) => {
    const dialog = get().dialogs.find(d => d.id === id);
    if (!dialog) {
      return;
    }

    try {
      get().setLoading(id, true);
      await dialog.action.onClick(inputValue);
      get().removeDialog(id);
    } catch (error) {
      console.error('Dialog action failed:', error);
      get().setLoading(id, false);
    }
  }
}));

// API publique simplifiée
export const DialogManager = {
  add: (config: Omit<DialogConfig, 'id' | 'loading'>) => {
    useDialogManager.getState().addDialog(config);
  },

  remove: (id: string) => {
    useDialogManager.getState().removeDialog(id);
  },

  clear: () => {
    useDialogManager.setState({ dialogs: [] });
  }
};
```

### **2. Composant de Rendu (dialog-manager-dialog.tsx)**

```tsx
'use client';

import { Loader2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { DialogConfig, useDialogManager } from './dialog-manager-store';

type DialogManagerDialogProps = {
  dialog: DialogConfig;
};

export function DialogManagerDialog({ dialog }: DialogManagerDialogProps) {
  const t = useTranslations('dialogs');
  const { executeAction, removeDialog } = useDialogManager();
  const [inputValue, setInputValue] = useState('');
  const [confirmValue, setConfirmValue] = useState('');

  const isConfirmValid = !dialog.confirmText || confirmValue === dialog.confirmText;
  const isInputValid = !dialog.input?.required || inputValue.trim().length > 0;
  const canExecute = isConfirmValid && isInputValid && !dialog.loading;

  const handleAction = async () => {
    if (!canExecute) {
      return;
    }
    await executeAction(dialog.id, inputValue);
  };

  const handleCancel = () => {
    if (dialog.cancel?.onClick) {
      dialog.cancel.onClick();
    } else {
      removeDialog(dialog.id);
    }
  };

  return (
    <AlertDialog open={true} onOpenChange={() => handleCancel()}>
      <AlertDialogContent className="sm:min-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-3">
            {dialog.icon && (
              <dialog.icon className={`h-6 w-6 ${getIconColor(dialog.style)}`} />
            )}
            <div>
              <AlertDialogTitle className={getTitleColor(dialog.style)}>
                {dialog.title}
              </AlertDialogTitle>
              {dialog.description && (
                <AlertDialogDescription className="mt-2">
                  {dialog.description}
                </AlertDialogDescription>
              )}
            </div>
          </div>
        </AlertDialogHeader>

        <div className="space-y-4">
          {/* Input générique */}
          {dialog.input && (
            <div className="space-y-2">
              <Label htmlFor="dialog-input" className="text-sm font-medium">
                {dialog.input.label}
                {dialog.input.required && <span className="text-destructive">*</span>}
              </Label>
              <Input
                id="dialog-input"
                type={dialog.input.type || 'text'}
                placeholder={dialog.input.placeholder}
                value={inputValue}
                onChange={e => setInputValue(e.target.value)}
                disabled={dialog.loading}
              />
            </div>
          )}

          {/* Confirmation par texte */}
          {dialog.confirmText && (
            <div className="space-y-2">
              <Label htmlFor="confirm-input" className="text-sm font-medium">
                {t('confirmByTyping')}
                {' '}
                <code className="bg-muted px-1 rounded">{dialog.confirmText}</code>
              </Label>
              <Input
                id="confirm-input"
                placeholder={dialog.confirmText}
                value={confirmValue}
                onChange={e => setConfirmValue(e.target.value)}
                disabled={dialog.loading}
              />
            </div>
          )}
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancel} disabled={dialog.loading}>
            {dialog.cancel?.label || t('cancel')}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleAction}
            disabled={!canExecute}
            className={getActionButtonClass(dialog.style)}
          >
            {dialog.loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {dialog.action.label}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

// Fonctions utilitaires pour les styles
function getIconColor(style?: string): string {
  switch (style) {
    case 'destructive': return 'text-destructive';
    case 'warning': return 'text-warning';
    case 'info': return 'text-info';
    default: return 'text-muted-foreground';
  }
}

function getTitleColor(style?: string): string {
  switch (style) {
    case 'destructive': return 'text-destructive';
    default: return '';
  }
}

function getActionButtonClass(style?: string): string {
  switch (style) {
    case 'destructive': return 'bg-destructive text-destructive-foreground hover:bg-destructive/90';
    case 'warning': return 'bg-warning text-warning-foreground hover:bg-warning/90';
    default: return '';
  }
}
```

### **3. Renderer Global (dialog-manager-renderer.tsx)**

```tsx
'use client';

import { DialogManagerDialog } from './dialog-manager-dialog';
import { useDialogManager } from './dialog-manager-store';

export function DialogManagerRenderer() {
  const { dialogs } = useDialogManager();

  // Affiche seulement le premier dialog de la queue
  const currentDialog = dialogs[0];

  if (!currentDialog) {
    return null;
  }

  return <DialogManagerDialog dialog={currentDialog} />;
}
```

## 🚀 Intégration dans l'Application

### **1. Ajout dans les Providers**

```tsx
// app/providers.tsx
import { DialogManagerRenderer } from '@/components/dialog-manager/dialog-manager-renderer';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <div>
      {children}
      {/* Renderer global pour les dialogs */}
      <DialogManagerRenderer />
    </div>
  );
}
```

### **2. Traductions (locales/en.json)**

```json
{
  "dialogs": {
    "cancel": "Cancel",
    "confirmByTyping": "Type the following to confirm:",
    "loading": "Processing...",
    "errors": {
      "actionFailed": "Action failed. Please try again."
    }
  }
}
```

## 📝 Exemples d'Utilisation

### **1. Dialog de Confirmation Simple**

```tsx
import { Trash2 } from 'lucide-react';
import { DialogManager } from '@/components/dialog-manager/dialog-manager-store';

function DeleteAccountButton() {
  const handleDelete = async () => {
    // Logique de suppression
    await deleteUserAccount();
  };

  const showDeleteDialog = () => {
    DialogManager.add({
      title: 'Delete Account',
      description: 'This action cannot be undone. All your data will be permanently deleted.',
      icon: Trash2,
      style: 'destructive',
      action: {
        label: 'Delete Account',
        onClick: handleDelete,
        variant: 'destructive'
      }
    });
  };

  return (
    <Button variant="destructive" onClick={showDeleteDialog}>
      Delete Account
    </Button>
  );
}
```

### **2. Dialog avec Confirmation par Texte**

```tsx
function DeleteProjectButton({ projectName }: { projectName: string }) {
  const handleDelete = async () => {
    await deleteProject(projectName);
  };

  const showDeleteDialog = () => {
    DialogManager.add({
      title: `Delete "${projectName}"`,
      description: 'This will permanently delete the project and all associated data.',
      confirmText: projectName, // L'utilisateur doit taper le nom du projet
      style: 'destructive',
      action: {
        label: 'Delete Project',
        onClick: handleDelete
      }
    });
  };

  return (
    <Button variant="destructive" onClick={showDeleteDialog}>
      Delete Project
    </Button>
  );
}
```

### **3. Dialog avec Input Générique**

```tsx
function RenameAccountButton({ accountId }: { accountId: string }) {
  const handleRename = async (newName: string) => {
    await renameAccount(accountId, newName);
  };

  const showRenameDialog = () => {
    DialogManager.add({
      title: 'Rename Account',
      description: 'Enter a new name for this account.',
      input: {
        label: 'Account Name',
        placeholder: 'Enter new name...',
        required: true
      },
      action: {
        label: 'Rename',
        onClick: handleRename
      }
    });
  };

  return (
    <Button variant="secondary" onClick={showRenameDialog}>
      Rename Account
    </Button>
  );
}
```

## ✅ Avantages du Système

1. **Code Réduit**: 5 lignes au lieu de 20+ lignes de JSX
2. **Réutilisabilité**: Configuration simple et cohérente
3. **Gestion d'État**: État global avec Zustand
4. **Fonctionnalités Avancées**: Confirmation par texte, inputs, loading states
5. **Type Safety**: TypeScript complet
6. **Internationalisation**: Support i18n intégré
7. **Accessibilité**: Composants shadcn/ui accessibles
8. **Testabilité**: Logique séparée et testable

## 🚀 Utilisation avec le Hook Personnalisé

```tsx
import { useDialogManager } from '@/hooks/use-dialog-manager';

function AccountManagement() {
  const dialog = useDialogManager();

  const handleDeleteAccount = () => {
    dialog.deleteAccount({
      accountHandle: '@user_account',
      requireConfirmation: true,
      onDelete: async () => {
        await deleteAccountAPI();
        // Redirection ou mise à jour de l'état
      }
    });
  };

  const handleRename = () => {
    dialog.renameAccount({
      currentName: 'Current Name',
      onRename: async (newName) => {
        await renameAccountAPI(newName);
      }
    });
  };

  const handleClaimTokens = () => {
    dialog.claimTokens({
      amount: '1,412.51',
      onClaim: async () => {
        await claimTokensAPI();
        dialog.showSuccess({
          description: 'Tokens claimed successfully!'
        });
      }
    });
  };

  return (
    <div className="space-y-4">
      <Button variant="destructive" onClick={handleDeleteAccount}>
        Delete Account
      </Button>
      <Button variant="secondary" onClick={handleRename}>
        Rename Account
      </Button>
      <Button onClick={handleClaimTokens}>
        Claim UMA Tokens
      </Button>
    </div>
  );
}
```

## 🧪 Tests et Qualité

### **Tests Unitaires**
```bash
# Exécuter les tests du DialogManager
npm test dialog-manager

# Tests avec couverture
npm test dialog-manager -- --coverage
```

### **Tests d'Intégration**
```tsx
// Exemple de test d'intégration
import { fireEvent, render, screen } from '@testing-library/react';
import { DialogManagerRenderer } from '@/components/dialog-manager/dialog-manager-renderer';
import { DialogManager } from '@/components/dialog-manager/dialog-manager-store';

test('should handle complete dialog workflow', async () => {
  const onConfirm = vi.fn();

  render(<DialogManagerRenderer />);

  // Ajouter un dialog
  DialogManager.add({
    title: 'Test Dialog',
    action: { label: 'Confirm', onClick: onConfirm }
  });

  // Vérifier l'affichage
  expect(screen.getByText('Test Dialog')).toBeInTheDocument();

  // Interagir
  fireEvent.click(screen.getByRole('button', { name: 'Confirm' }));

  // Vérifier l'exécution
  expect(onConfirm).toHaveBeenCalled();
});
```

## 📋 Checklist d'Implémentation

### **Installation**
- [ ] Installer les dépendances: `zustand`, `nanoid`
- [ ] Créer les fichiers du système DialogManager
- [ ] Ajouter le renderer dans le layout principal
- [ ] Configurer les traductions (en/fr)

### **Configuration**
- [ ] Intégrer dans `app/[locale]/layout.tsx`
- [ ] Ajouter les traductions dans `locales/en.json` et `locales/fr.json`
- [ ] Créer le hook personnalisé `useDialogManager`
- [ ] Configurer les tests avec Vitest

### **Utilisation**
- [ ] Remplacer les AlertDialog existants par DialogManager
- [ ] Utiliser le hook personnalisé pour les cas d'usage courants
- [ ] Tester tous les types de dialogs (confirmation, input, etc.)
- [ ] Vérifier l'accessibilité et la navigation clavier

### **Tests**
- [ ] Tests unitaires pour le store Zustand
- [ ] Tests de composants pour le renderer
- [ ] Tests d'intégration pour les workflows complets
- [ ] Tests de validation et d'erreurs
- [ ] Tests de priorité et de queue

## 🔧 Personnalisation Avancée

### **Styles Personnalisés**
```tsx
// Ajouter des styles personnalisés
DialogManager.add({
  title: 'Custom Dialog',
  style: 'warning', // ou 'info', 'success', 'destructive'
  action: {
    label: 'Action',
    onClick: () => {}
  }
});
```

### **Validation Complexe**
```tsx
// Validation avec logique métier
DialogManager.prompt({
  title: 'Enter Amount',
  input: {
    label: 'Amount ($)',
    type: 'number',
    validation: (value) => {
      const num = Number.parseFloat(value);
      if (isNaN(num)) {
        return 'Please enter a valid number';
      }
      if (num < 10) {
        return 'Minimum amount is $10';
      }
      if (num > 1000) {
        return 'Maximum amount is $1000';
      }
      if (num % 0.01 !== 0) {
        return 'Please use 2 decimal places';
      }
      return null;
    }
  },
  onSubmit: async (amount) => {
    await processPayment(Number.parseFloat(amount));
  }
});
```

### **Gestion d'Erreurs Avancée**
```tsx
// Gestion d'erreurs avec retry
const handleAsyncAction = async () => {
  try {
    await riskyOperation();
  } catch (error) {
    dialog.showError({
      title: 'Operation Failed',
      description: error.message,
      onRetry: handleAsyncAction // Retry automatique
    });
  }
};
```

## 📚 Ressources Supplémentaires

- **[Zustand Documentation](https://zustand-demo.pmnd.rs/)** - Gestion d'état
- **[shadcn/ui AlertDialog](https://ui.shadcn.com/docs/components/alert-dialog)** - Composants UI
- **[Vitest Testing](https://vitest.dev/)** - Framework de test
- **[next-intl](https://next-intl-docs.vercel.app/)** - Internationalisation

---

**Le système DialogManager transforme la gestion des dialogs en Hexa TikPay, offrant une API simple, puissante et entièrement typée pour toutes les interactions utilisateur.**
