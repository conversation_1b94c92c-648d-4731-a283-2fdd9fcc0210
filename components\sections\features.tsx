"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { CheckCircle } from "lucide-react";
import { features } from "@/lib/constants";
import { FadeInWrapper, SlideInWrapper, StaggeredWrapper } from "@/components/ui/parallax-wrapper";

export function FeaturesSection() {
  const [activeFeature, setActiveFeature] = useState("0");

  return (
    <section id="features" className="section-spacing bg-gradient-to-b from-background to-muted/20">
      <div className="section-container">
        {/* Section Header */}
        <SlideInWrapper className="section-header">
          <Badge variant="outline" className="mb-4">
            Account Packages
          </Badge>
          <h2 className="heading-section mb-6">
            Choose the perfect
            <span className="text-primary"> TikTok account</span>
          </h2>
          <p className="text-lead min-w-3xl mx-auto">
            From starter accounts to premium influencer profiles, we have the perfect TikTok account
            to match your goals and accelerate your social media success.
          </p>
        </SlideInWrapper>

        {/* Features Grid */}
        <StaggeredWrapper
          className="grid-3-cols mb-16 section-content-wide"
          staggerDelay={0.2}
        >
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <Card
                key={feature.id}
                className="relative overflow-hidden border-0 bg-background/50 backdrop-blur-sm hover:bg-background/80 transition-all duration-300 hover:scale-105 hover:shadow-xl"
              >
                <CardHeader className="pb-4">
                  <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4 hover:bg-primary/20 transition-colors">
                    <IconComponent className="w-6 h-6 text-primary" />
                  </div>
                  <CardTitle className="text-xl font-semibold hover:text-primary transition-colors">
                    {feature.title}
                  </CardTitle>
                  <CardDescription className="text-muted-foreground">
                    {feature.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="flex items-center text-sm text-muted-foreground">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>

                {/* Hover Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-blue-500/5 opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
              </Card>
            );
          })}
        </StaggeredWrapper>

        {/* Interactive Feature Showcase */}
        <FadeInWrapper delay={0.6} className="min-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Explore account categories
            </h3>
            <p className="text-muted-foreground">
              Discover the perfect TikTok account to accelerate your influence and monetization
            </p>
          </div>

          <Tabs value={activeFeature} onValueChange={setActiveFeature} className="w-full">
            <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6 mb-8">
              {features.map((feature) => {
                const IconComponent = feature.icon;
                return (
                  <TabsTrigger
                    key={feature.id}
                    value={feature.id}
                    className="flex flex-col items-center gap-2 p-4 h-auto"
                  >
                    <IconComponent className="w-5 h-5" />
                    <span className="text-xs font-medium hidden sm:block">
                      {feature.title.split(' ')[0]}
                    </span>
                  </TabsTrigger>
                );
              })}
            </TabsList>

            {features.map((feature) => {
              const IconComponent = feature.icon;
              return (
                <TabsContent key={feature.id} value={feature.id} className="mt-8">
                  <Card className="border-0 bg-gradient-to-br from-background to-muted/20">
                    <CardContent className="p-8">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                        <div>
                          <div className="flex items-center gap-3 mb-4">
                            <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center">
                              <IconComponent className="w-6 h-6 text-primary" />
                            </div>
                            <h4 className="text-2xl font-bold">{feature.title}</h4>
                          </div>
                          <p className="text-lg text-muted-foreground mb-6">
                            {feature.description}
                          </p>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            {feature.benefits.map((benefit, index) => (
                              <div key={index} className="flex items-center text-sm">
                                <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                                {benefit}
                              </div>
                            ))}
                          </div>
                        </div>
                        <div className="relative">
                          <div className="aspect-video bg-gradient-to-br from-primary/10 to-blue-500/10 rounded-lg flex items-center justify-center">
                            <IconComponent className="w-24 h-24 text-primary/30" />
                          </div>
                          {/* Placeholder for feature demo/screenshot */}
                          <div className="absolute inset-0 bg-gradient-to-t from-background/20 to-transparent rounded-lg" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              );
            })}
          </Tabs>
        </FadeInWrapper>
      </div>
    </section>
  );
}
