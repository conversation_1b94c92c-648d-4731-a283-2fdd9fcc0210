import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { cache } from "react";

/**
 * Cached session getter for server components
 * Uses React's cache to avoid multiple session fetches in the same request
 */
export const getSession = cache(async () => {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });
    return session;
  } catch (error) {
    console.error("Failed to get session:", error);
    return null;
  }
});

/**
 * Cached user getter for server components
 */
export const getUser = cache(async () => {
  const session = await getSession();
  return session?.user || null;
});

/**
 * Check if user is authenticated
 */
export const isAuthenticated = cache(async () => {
  const session = await getSession();
  return !!session;
});

/**
 * Require authentication - throws if not authenticated
 */
export const requireAuth = cache(async () => {
  const session = await getSession();
  if (!session) {
    throw new Error("Authentication required");
  }
  return session;
});

/**
 * Get session with error handling for server actions
 */
export const getSessionForAction = async () => {
  try {
    return await getSession();
  } catch (error) {
    console.error("Session error in server action:", error);
    return null;
  }
};

/**
 * Type-safe session type
 */
export type SessionData = Awaited<ReturnType<typeof getSession>>;
export type UserData = Awaited<ReturnType<typeof getUser>>;
