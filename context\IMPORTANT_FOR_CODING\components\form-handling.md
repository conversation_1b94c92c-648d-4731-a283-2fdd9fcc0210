# 📝 Form Handling Patterns

This guide covers form handling best practices for the Hexa TikPay project using React Hook Form with Zod validation.

## 📋 Overview

Our form implementation follows these principles:
- **Type Safety**: Full TypeScript integration with Zod validation
- **Performance**: Optimized rendering with React Hook Form
- **Accessibility**: WCAG-compliant form controls
- **Error Handling**: Comprehensive validation and error display
- **User Experience**: Responsive feedback and intuitive interactions

## 🧩 Form Components

### Basic Form Structure

```tsx
'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

// Define form schema
const formSchema = z.object({
  email: z.string().email('Please enter a valid email'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

// Infer type from schema
type FormValues = z.infer<typeof formSchema>;

export function LoginForm() {
  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  // Form submission handler
  async function onSubmit(values: FormValues) {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Login failed');
      }

      toast.success('Login successful');
      window.location.href = '/dashboard';
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Login failed');
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <Input type="password" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full" disabled={form.formState.isSubmitting}>
          {form.formState.isSubmitting ? 'Logging in...' : 'Log in'}
        </Button>
      </form>
    </Form>
  );
}
```

## 🔍 Validation Patterns

### Complex Validation Rules

```typescript
// Define schema with complex validation
const signupSchema = z.object({
  email: z.string().email('Please enter a valid email'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/\d/, 'Password must contain at least one number')
    .regex(/[^A-Z0-9]/i, 'Password must contain at least one special character'),
  confirmPassword: z.string(),
  terms: z.boolean().refine(val => val === true, {
    message: 'You must accept the terms and conditions',
  }),
})
  .refine(data => data.password === data.confirmPassword, {
    message: 'Passwords don\'t match',
    path: ['confirmPassword'],
  });
```

### Conditional Validation

```typescript
// Schema with conditional validation
const paymentSchema = z.object({
  amount: z.number().min(1, 'Amount must be greater than 0'),
  paymentMethod: z.enum(['credit_card', 'bank_transfer', 'paypal']),

  // Credit card details (only required if payment method is credit_card)
  cardNumber: z.string().optional(),
  expiryDate: z.string().optional(),
  cvv: z.string().optional(),

  // Bank details (only required if payment method is bank_transfer)
  accountNumber: z.string().optional(),
  routingNumber: z.string().optional(),
})
  .refine(
    (data) => {
      if (data.paymentMethod === 'credit_card') {
        return !!data.cardNumber && !!data.expiryDate && !!data.cvv;
      }
      return true;
    },
    {
      message: 'Credit card details are required',
      path: ['cardNumber'],
    }
  )
  .refine(
    (data) => {
      if (data.paymentMethod === 'bank_transfer') {
        return !!data.accountNumber && !!data.routingNumber;
      }
      return true;
    },
    {
      message: 'Bank account details are required',
      path: ['accountNumber'],
    }
  );
```

## 🔄 Form State Management

### Form with Loading State

```tsx
'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';

const contactSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
});

type ContactFormValues = z.infer<typeof contactSchema>;

export function ContactForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<ContactFormValues>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      name: '',
      email: '',
      message: '',
    },
  });

  async function onSubmit(values: ContactFormValues) {
    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to send message');
      }

      setIsSuccess(true);
      form.reset();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <div className="space-y-6">
      {isSuccess
        ? (
            <div className="bg-green-50 p-4 rounded-md border border-green-200">
              <p className="text-green-800">Your message has been sent successfully!</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => setIsSuccess(false)}
              >
                Send another message
              </Button>
            </div>
          )
        : (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                {error && (
                  <div className="bg-red-50 p-4 rounded-md border border-red-200">
                    <p className="text-red-800">{error}</p>
                  </div>
                )}

                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Your name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="message"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Message</FormLabel>
                      <FormControl>
                        <textarea
                          className="min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                          placeholder="Your message"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </Button>
              </form>
            </Form>
          )}
    </div>
  );
}
```

### Form with Dynamic Fields

```tsx
'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useFieldArray, useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { PlusIcon, XIcon } from '@/components/ui/icons';
import { Input } from '@/components/ui/input';

// Define schema with array field
const productSchema = z.object({
  name: z.string().min(2, 'Product name is required'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  price: z.coerce.number().min(0.01, 'Price must be greater than 0'),
  variants: z.array(
    z.object({
      name: z.string().min(1, 'Variant name is required'),
      price: z.coerce.number().min(0, 'Price must be positive'),
    })
  ).optional().default([]),
});

type ProductFormValues = z.infer<typeof productSchema>;

export function ProductForm() {
  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: '',
      description: '',
      price: 0,
      variants: [{ name: '', price: 0 }],
    },
  });

  // Use field array for dynamic variants
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'variants',
  });

  async function onSubmit(values: ProductFormValues) {
    console.log(values);
    // API submission logic
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Product Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter product name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <textarea
                  className="min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  placeholder="Product description"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="price"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Base Price</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Product Variants</h3>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => append({ name: '', price: 0 })}
            >
              <PlusIcon className="mr-2 h-4 w-4" />
              Add Variant
            </Button>
          </div>

          {fields.map((field, index) => (
            <div key={field.id} className="flex gap-4 items-end">
              <FormField
                control={form.control}
                name={`variants.${index}.name`}
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel>Variant Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Small, Red, etc." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`variants.${index}.price`}
                render={({ field }) => (
                  <FormItem className="w-32">
                    <FormLabel>Price</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="mb-2"
                onClick={() => remove(index)}
                disabled={fields.length === 1}
              >
                <XIcon className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>

        <Button type="submit">Save Product</Button>
      </form>
    </Form>
  );
}
```

## 🔄 Form Submission Patterns

### API Submission with Error Handling

```tsx
async function onSubmit(values: FormValues) {
  setIsSubmitting(true);
  setFormError(null);

  try {
    const response = await fetch('/api/resource', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(values),
    });

    const data = await response.json();

    if (!response.ok) {
      // Handle different error types
      if (response.status === 400) {
        // Validation error from the server
        if (data.errors) {
          // Set form errors
          Object.entries(data.errors).forEach(([field, message]) => {
            form.setError(field as any, {
              type: 'server',
              message: message as string,
            });
          });
          throw new Error('Please correct the errors in the form');
        }
      } else if (response.status === 401) {
        // Authentication error
        throw new Error('You must be logged in to perform this action');
      } else if (response.status === 403) {
        // Authorization error
        throw new Error('You do not have permission to perform this action');
      } else if (response.status === 409) {
        // Conflict error
        throw new Error(data.message || 'Resource already exists');
      } else {
        // Generic error
        throw new Error(data.message || 'An error occurred');
      }
    }

    // Success handling
    toast.success('Form submitted successfully');
    router.push('/success-page');
  } catch (error) {
    // Set general form error
    setFormError(error instanceof Error ? error.message : 'An unexpected error occurred');
    // Scroll to the top of the form to show the error
    formRef.current?.scrollIntoView({ behavior: 'smooth' });
  } finally {
    setIsSubmitting(false);
  }
}
```

### Form with Confirmation Dialog

```tsx
'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';

const deleteAccountSchema = z.object({
  confirmText: z.string().refine(val => val === 'DELETE', {
    message: 'Please type \'DELETE\' to confirm',
  }),
  reason: z.string().min(10, 'Please provide a reason with at least 10 characters'),
});

type DeleteAccountFormValues = z.infer<typeof deleteAccountSchema>;

export function DeleteAccountForm() {
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const formValues = useRef<DeleteAccountFormValues | null>(null);

  const form = useForm<DeleteAccountFormValues>({
    resolver: zodResolver(deleteAccountSchema),
    defaultValues: {
      confirmText: '',
      reason: '',
    },
  });

  function onSubmit(values: DeleteAccountFormValues) {
    // Store form values for confirmation
    formValues.current = values;
    // Open confirmation dialog
    setIsConfirmOpen(true);
  }

  async function handleConfirmDelete() {
    if (!formValues.current) {
      return;
    }

    setIsSubmitting(true);

    try {
      // API call to delete account
      const response = await fetch('/api/account/delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          reason: formValues.current.reason,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to delete account');
      }

      // Redirect to logout or confirmation page
      window.location.href = '/account-deleted';
    } catch (error) {
      console.error(error);
      setIsConfirmOpen(false);
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <>
      <div className="space-y-6">
        <div className="p-4 bg-red-50 border border-red-200 rounded-md">
          <h3 className="text-lg font-medium text-red-800">Delete Account</h3>
          <p className="mt-1 text-sm text-red-700">
            This action is permanent and cannot be undone. All your data will be permanently deleted.
          </p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Please tell us why you're leaving</FormLabel>
                  <FormControl>
                    <textarea
                      className="min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                      placeholder="Your reason"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmText"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Type "DELETE" to confirm</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="DELETE"
                      className="uppercase"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" variant="destructive">
              Delete Account
            </Button>
          </form>
        </Form>
      </div>

      <Dialog open={isConfirmOpen} onOpenChange={setIsConfirmOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you absolutely sure?</DialogTitle>
            <DialogDescription>
              This action is permanent and cannot be undone. Your account and all associated data will be permanently deleted.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter className="gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setIsConfirmOpen(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirmDelete}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Deleting...' : 'Yes, delete my account'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
```

## 📋 Form Best Practices Checklist

- [ ] Use React Hook Form for performance optimization
- [ ] Implement Zod validation schemas for type safety
- [ ] Provide meaningful error messages for validation failures
- [ ] Show loading states during form submission
- [ ] Handle all possible API response scenarios
- [ ] Implement proper focus management for accessibility
- [ ] Use proper HTML5 input types (email, number, tel, etc.)
- [ ] Add appropriate ARIA attributes for screen readers
- [ ] Implement proper keyboard navigation
- [ ] Use consistent form styling across the application
- [ ] Group related form fields logically
- [ ] Provide clear submission success/failure feedback
- [ ] Prevent multiple form submissions
- [ ] Reset form state appropriately after submission
- [ ] Implement proper form validation timing (onBlur, onChange, etc.)
- [ ] Handle file uploads with proper validation and progress indicators
- [ ] Use confirmation dialogs for destructive actions
- [ ] Implement form autosave for long forms when appropriate
- [ ] Add support for form steps/wizard for complex forms
- [ ] Test forms for accessibility compliance
