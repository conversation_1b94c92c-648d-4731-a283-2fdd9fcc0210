import { Metada<PERSON> } from "next";
import { ResetPasswordForm } from "@/components/auth/reset-password-form";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Reset Password",
  description: "Set a new password for your TikPay account",
};

export default function ResetPasswordPage() {
  return (
    <div className="space-y-6">
      <div className="space-y-2 text-center">
        <h1 className="text-2xl font-semibold tracking-tight">
          Reset your password
        </h1>
        <p className="text-sm text-muted-foreground">
          Enter your new password below
        </p>
      </div>

      <ResetPasswordForm />

      <div className="text-center text-sm">
        <Link
          href="/auth/signin"
          className="font-medium text-primary hover:underline"
        >
          Back to sign in
        </Link>
      </div>
    </div>
  );
}
