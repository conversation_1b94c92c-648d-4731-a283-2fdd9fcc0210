"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Mail, CheckCircle, AlertCircle } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";

export function EmailVerificationForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [verificationError, setVerificationError] = useState<string | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  useEffect(() => {
    if (token) {
      verifyEmail(token);
    }
  }, [token]);

  const verifyEmail = async (verificationToken: string) => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/auth/verify-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          token: verificationToken,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to verify email");
      }

      setIsVerified(true);
      toast.success("Email verified!", {
        description: "Your account has been successfully verified",
      });

      // Redirect to sign in after a short delay
      setTimeout(() => {
        router.push("/auth/signin");
      }, 2000);
    } catch (error) {
      setVerificationError("Failed to verify email. The link may be invalid or expired.");
      toast.error("Verification failed", {
        description: "Please try again or request a new verification email",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resendVerificationEmail = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/auth/send-verification-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to send verification email");
      }

      toast.success("Verification email sent!", {
        description: "Check your email for the new verification link",
      });
    } catch (error) {
      toast.error("Something went wrong", {
        description: "Please try again later",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isVerified) {
    return (
      <div className="space-y-6 text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
          <CheckCircle className="h-6 w-6 text-green-600" />
        </div>
        <div className="space-y-2">
          <h3 className="text-lg font-medium">Email verified!</h3>
          <p className="text-sm text-muted-foreground">
            Your account has been successfully verified. You'll be redirected to sign in shortly.
          </p>
        </div>
        <Button
          onClick={() => router.push("/auth/signin")}
          className="w-full"
        >
          Continue to sign in
        </Button>
      </div>
    );
  }

  if (verificationError) {
    return (
      <div className="space-y-6 text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
          <AlertCircle className="h-6 w-6 text-red-600" />
        </div>
        <div className="space-y-2">
          <h3 className="text-lg font-medium">Verification failed</h3>
          <p className="text-sm text-muted-foreground">
            {verificationError}
          </p>
        </div>
        <div className="space-y-3">
          <Button
            onClick={resendVerificationEmail}
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? "Sending..." : "Send new verification email"}
          </Button>
          <Button
            variant="outline"
            onClick={() => router.push("/auth/signin")}
            className="w-full"
          >
            Back to sign in
          </Button>
        </div>
      </div>
    );
  }

  if (token && isLoading) {
    return (
      <div className="space-y-6 text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
          <Mail className="h-6 w-6 text-blue-600 animate-pulse" />
        </div>
        <div className="space-y-2">
          <h3 className="text-lg font-medium">Verifying your email...</h3>
          <p className="text-sm text-muted-foreground">
            Please wait while we verify your email address.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 text-center">
      <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
        <Mail className="h-6 w-6 text-blue-600" />
      </div>
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Check your email</h3>
        <p className="text-sm text-muted-foreground">
          We've sent a verification link to your email address. Click the link to verify your account.
        </p>
      </div>
      <div className="space-y-3">
        <Button
          onClick={resendVerificationEmail}
          disabled={isLoading}
          variant="outline"
          className="w-full"
        >
          {isLoading ? "Sending..." : "Resend verification email"}
        </Button>
        <Button
          variant="ghost"
          onClick={() => router.push("/auth/signin")}
          className="w-full"
        >
          Back to sign in
        </Button>
      </div>
    </div>
  );
}
