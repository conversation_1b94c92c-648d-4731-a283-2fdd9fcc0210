import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { organization } from "better-auth/plugins";
import { nextCookies } from "better-auth/next-js";
import { db } from "./db";
import { env } from "./env";

export const auth = betterAuth({
  database: prismaAdapter(db, {
    provider: "postgresql",
  }),

  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
  },

  socialProviders: {
    ...(env.GOOGLE_CLIENT_ID && env.GOOGLE_CLIENT_SECRET && {
      google: {
        clientId: env.GOOGLE_CLIENT_ID,
        clientSecret: env.GOOGLE_CLIENT_SECRET,
        mapProfileToUser: (profile) => ({
          firstName: profile.given_name,
          lastName: profile.family_name,
        }),
      },
    }),
  },

  plugins: [
    organization({
      allowUserToCreateOrganization: true,
      organizationLimit: 5, // Limit per user
      sendInvitationEmail: async (data) => {
        // TODO: Implement email sending with Resend
        console.log("Send invitation email:", data);
      },
    }),
    nextCookies(), // Must be last plugin
  ],

  secret: env.BETTER_AUTH_SECRET,
  baseURL: env.BETTER_AUTH_URL,

  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },

  // Security configurations
  security: {
    csrf: {
      enabled: true,
      cookieName: "__Host-better-auth.csrf-token",
      cookieOptions: {
        httpOnly: true,
        secure: env.NODE_ENV === "production",
        sameSite: "lax",
      },
    },
  },

  // Cookie configuration
  cookies: {
    sessionToken: {
      name: "better-auth.session-token",
      options: {
        httpOnly: true,
        secure: env.NODE_ENV === "production",
        sameSite: "lax",
        path: "/",
        maxAge: 60 * 60 * 24 * 7, // 7 days
      },
    },
  },

  // Rate limiting
  rateLimit: {
    window: 60, // 1 minute
    max: 10, // 10 requests per minute
    storage: "memory",
  },

  user: {
    additionalFields: {
      emailVerified: {
        type: "boolean",
        defaultValue: false,
      },
    },
  },

  callbacks: {
    async signUp({ user }: { user: any }) {
      // Custom logic after user signs up
      console.log("User signed up:", user.email);
      return user;
    },

    async signIn({ user, session }: { user: any; session: any }) {
      // Custom logic after user signs in
      console.log("User signed in:", user.email);
      return { user, session };
    },
  },
});

export type Session = typeof auth.$Infer.Session;
